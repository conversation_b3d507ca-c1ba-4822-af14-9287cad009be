import 'package:flutter/material.dart';
import 'views/home_screen.dart';
import 'views/map_screen.dart';
import 'views/places_screen.dart';
import 'views/events_screen.dart';
import 'views/cuisine_screen.dart';
import 'views/budget_screen.dart';
import 'views/diary_screen.dart';
import 'views/dictionary_screen.dart';
import 'views/settings_screen.dart';
import 'views/smart_city_screen.dart';

void main() {
  runApp(const CroatiaTravelApp());
}

class CroatiaTravelApp extends StatelessWidget {
  const CroatiaTravelApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Croatia Travel App',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2E5984), // Adriatic blue
          brightness: Brightness.light,
        ),
        scaffoldBackgroundColor: const Color(
          0xFFF8F6F3,
        ), // Warm cream background
        cardColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF2E5984),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        useMaterial3: true,
      ),
      home: const MainNavigationScreen(),
    );
  }
}

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 10, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ADRIATIC DIARY'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.home), text: 'Domů'),
            Tab(icon: Icon(Icons.map), text: 'Mapa'),
            Tab(icon: Icon(Icons.place), text: 'Místa'),
            Tab(icon: Icon(Icons.event), text: 'Události'),
            Tab(icon: Icon(Icons.restaurant), text: 'Kuchyně'),
            Tab(icon: Icon(Icons.account_balance_wallet), text: 'Rozpočet'),
            Tab(icon: Icon(Icons.book), text: 'Deník'),
            Tab(icon: Icon(Icons.translate), text: 'Slovník'),
            Tab(icon: Icon(Icons.location_city), text: 'Město'),
            Tab(icon: Icon(Icons.settings), text: 'Nastavení'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          HomeScreen(),
          MapScreen(),
          PlacesScreen(),
          EventsScreen(),
          CuisineScreen(),
          BudgetScreen(),
          DiaryScreen(),
          DictionaryScreen(),
          SmartCityScreen(),
          SettingsScreen(),
        ],
      ),
    );
  }
}
