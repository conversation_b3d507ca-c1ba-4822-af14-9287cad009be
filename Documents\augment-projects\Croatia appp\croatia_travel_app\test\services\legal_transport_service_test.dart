import 'package:flutter_test/flutter_test.dart';
import 'package:croatia_travel_app/services/legal_transport_service.dart';
import 'package:croatia_travel_app/models/transport_simple.dart';

void main() {
  group('LegalTransportService Tests', () {
    late LegalTransportService service;

    setUp(() {
      service = LegalTransportService();
    });

    test('should initialize without errors', () async {
      expect(() async => await service.initialize(), returnsNormally);
    });

    test('should return stops for Zagreb', () async {
      await service.initialize();
      
      final stops = await service.getStopsForCity('zagreb');
      
      expect(stops, isA<List<TransportStop>>());
      expect(stops, isNotEmpty);
      
      final firstStop = stops.first;
      expect(firstStop.name, isNotEmpty);
      expect(firstStop.city, equals('zagreb'));
      expect(firstStop.latitude, greaterThan(45.0));
      expect(firstStop.longitude, greaterThan(15.0));
    });

    test('should return real-time arrivals', () async {
      await service.initialize();
      
      final arrivals = await service.getRealTimeArrivals('test_stop', 'zagreb');
      
      expect(arrivals, isA<List<RealTimeArrival>>());
      // Může být prázdné pokud nejsou crowdsourced data
    });

    test('should plan basic route', () async {
      await service.initialize();
      
      final routes = await service.planRoute(
        fromLat: 45.815,
        fromLng: 15.982,
        toLat: 45.813,
        toLng: 15.977,
      );
      
      expect(routes, isA<List<TransportRoute>>());
    });

    test('should report user data successfully', () async {
      await service.initialize();
      
      final result = await service.reportUserData(
        type: UserReportType.delay,
        data: {
          'stopId': 'test_stop',
          'routeNumber': '6',
          'delay': Duration(minutes: 5),
          'comment': 'Test delay report',
        },
        userId: 'test_user',
      );
      
      // Může být false pokud není backend nakonfigurován
      expect(result, isA<bool>());
    });

    test('should handle invalid city gracefully', () async {
      await service.initialize();
      
      final stops = await service.getStopsForCity('invalid_city');
      
      expect(stops, isA<List<TransportStop>>());
      // Měl by vrátit fallback data
    });

    test('should provide data quality info', () async {
      await service.initialize();
      
      final qualityInfo = service.getDataQualityInfo();
      
      expect(qualityInfo, isA<Map<String, DataQuality>>());
    });

    tearDown(() {
      service.dispose();
    });
  });
}
