import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  StreamSubscription<Position>? _positionSubscription;
  StreamController<Position>? _positionController;

  /// Získání aktuální polohy
  Future<Position> getCurrentPosition() async {
    final permission = await _checkLocationPermission();
    if (!permission) {
      throw Exception('Povolení k poloze bylo zamítnuto');
    }

    return await Geolocator.getCurrentPosition(
      locationSettings: const LocationSettings(accuracy: LocationAccuracy.high),
    );
  }

  /// Stream pro sledování polohy
  Stream<Position> get positionStream {
    _positionController ??= StreamController<Position>.broadcast();
    _startLocationTracking();
    return _positionController!.stream;
  }

  /// Kontrola povolení k poloze
  Future<bool> _checkLocationPermission() async {
    final status = await Permission.location.status;

    if (status.isDenied) {
      final result = await Permission.location.request();
      return result.isGranted;
    }

    return status.isGranted;
  }

  /// Spuštění sledování polohy
  void _startLocationTracking() {
    if (_positionSubscription != null) return;

    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Aktualizace každých 10 metrů
    );

    _positionSubscription =
        Geolocator.getPositionStream(locationSettings: locationSettings).listen(
          (position) {
            _positionController?.add(position);
          },
          onError: (error) {
            debugPrint('Chyba při sledování polohy: $error');
          },
        );
  }

  /// Zastavení sledování polohy
  void stopLocationTracking() {
    _positionSubscription?.cancel();
    _positionSubscription = null;
  }

  /// Výpočet vzdálenosti mezi dvěma body
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Kontrola, zda je uživatel v blízkosti místa
  bool isNearLocation(
    Position userPosition,
    double targetLatitude,
    double targetLongitude, {
    double radiusInMeters = 100,
  }) {
    final distance = calculateDistance(
      userPosition.latitude,
      userPosition.longitude,
      targetLatitude,
      targetLongitude,
    );

    return distance <= radiusInMeters;
  }

  /// Získání nejbližších míst
  List<T> getNearbyPlaces<T>(
    Position userPosition,
    List<T> places,
    double Function(T) getLatitude,
    double Function(T) getLongitude, {
    double maxDistanceInKm = 10,
  }) {
    final nearbyPlaces = <MapEntry<T, double>>[];

    for (final place in places) {
      final distance =
          calculateDistance(
            userPosition.latitude,
            userPosition.longitude,
            getLatitude(place),
            getLongitude(place),
          ) /
          1000; // Převod na kilometry

      if (distance <= maxDistanceInKm) {
        nearbyPlaces.add(MapEntry(place, distance));
      }
    }

    // Seřazení podle vzdálenosti
    nearbyPlaces.sort((a, b) => a.value.compareTo(b.value));

    return nearbyPlaces.map((entry) => entry.key).toList();
  }

  /// Optimalizované sledování polohy pro úsporný režim
  void enableBatterySaverMode() {
    stopLocationTracking();

    const batterySaverSettings = LocationSettings(
      accuracy: LocationAccuracy.low,
      distanceFilter: 100, // Aktualizace každých 100 metrů
    );

    _positionSubscription =
        Geolocator.getPositionStream(
          locationSettings: batterySaverSettings,
        ).listen((position) {
          _positionController?.add(position);
        });
  }

  /// Obnovení normálního sledování polohy
  void disableBatterySaverMode() {
    stopLocationTracking();
    _startLocationTracking();
  }

  void dispose() {
    stopLocationTracking();
    _positionController?.close();
    _positionController = null;
  }
}
