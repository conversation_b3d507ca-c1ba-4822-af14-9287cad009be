import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/transport_simple.dart';

/// Crowdsourcing služba pro získávání dopravních dat od uživatelů
class CrowdsourcingService {
  static final CrowdsourcingService _instance =
      CrowdsourcingService._internal();
  factory CrowdsourcingService() => _instance;
  CrowdsourcingService._internal();

  final Dio _dio = Dio();

  // Můžete použít Firebase, Supabase, nebo vlastní backend
  static const String _baseUrl = 'https://your-backend.com/api';

  /// Inicializace služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CroatiaTravel/1.0 (Crowdsourcing)',
      },
    );
  }

  /// Report<PERSON>n<PERSON> z<PERSON> od uživatele
  Future<bool> reportDelay({
    required String stopId,
    required String routeNumber,
    required Duration delay,
    required String userId,
    String? comment,
  }) async {
    try {
      final response = await _dio.post(
        '/reports/delay',
        data: {
          'stop_id': stopId,
          'route_number': routeNumber,
          'delay_minutes': delay.inMinutes,
          'user_id': userId,
          'comment': comment,
          'timestamp': DateTime.now().toIso8601String(),
          'source': 'user_report',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Chyba při reportování zpoždění: $e');
      return false;
    }
  }

  /// Reportování obsazenosti vozidla
  Future<bool> reportOccupancy({
    required String routeNumber,
    required String vehicleId,
    required OccupancyLevel occupancy,
    required String userId,
  }) async {
    try {
      final response = await _dio.post(
        '/reports/occupancy',
        data: {
          'route_number': routeNumber,
          'vehicle_id': vehicleId,
          'occupancy_level': occupancy.name,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Chyba při reportování obsazenosti: $e');
      return false;
    }
  }

  /// Přidání nové zastávky od uživatele
  Future<bool> submitNewStop({
    required String name,
    required double latitude,
    required double longitude,
    required String city,
    required String userId,
    List<String> routes = const [],
    String? description,
  }) async {
    try {
      final response = await _dio.post(
        '/stops/submit',
        data: {
          'name': name,
          'latitude': latitude,
          'longitude': longitude,
          'city': city,
          'routes': routes,
          'description': description,
          'submitted_by': userId,
          'timestamp': DateTime.now().toIso8601String(),
          'status': 'pending_verification',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Chyba při přidávání zastávky: $e');
      return false;
    }
  }

  /// Získání crowdsourced dat pro zastávku
  Future<List<CrowdsourcedArrival>> getCrowdsourcedArrivals(
    String stopId,
  ) async {
    try {
      final response = await _dio.get('/arrivals/crowdsourced/$stopId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['arrivals'] ?? [];
        return data.map((json) => CrowdsourcedArrival.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání crowdsourced dat: $e');
    }

    return [];
  }

  /// Získání uživatelských reportů pro město
  Future<List<UserReport>> getUserReports(String cityId) async {
    try {
      final response = await _dio.get('/reports/city/$cityId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['reports'] ?? [];
        return data.map((json) => UserReport.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání user reportů: $e');
    }

    return [];
  }

  /// Hlasování o kvalitě dat
  Future<bool> voteOnData({
    required String dataId,
    required DataType dataType,
    required bool isAccurate,
    required String userId,
  }) async {
    try {
      final response = await _dio.post(
        '/votes',
        data: {
          'data_id': dataId,
          'data_type': dataType.name,
          'is_accurate': isAccurate,
          'user_id': userId,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Chyba při hlasování: $e');
      return false;
    }
  }

  /// Získání statistik crowdsourcingu
  Future<CrowdsourcingStats?> getStats() async {
    try {
      final response = await _dio.get('/stats/crowdsourcing');

      if (response.statusCode == 200) {
        return CrowdsourcingStats.fromJson(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při načítání statistik: $e');
    }

    return null;
  }

  /// Gamifikace - získání bodů uživatele
  Future<UserPoints?> getUserPoints(String userId) async {
    try {
      final response = await _dio.get('/users/$userId/points');

      if (response.statusCode == 200) {
        return UserPoints.fromJson(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při načítání bodů: $e');
    }

    return null;
  }

  /// Offline queue pro reporty
  final List<Map<String, dynamic>> _offlineQueue = [];

  /// Přidání reportu do offline queue
  void addToOfflineQueue(Map<String, dynamic> report) {
    _offlineQueue.add({
      ...report,
      'queued_at': DateTime.now().toIso8601String(),
    });
  }

  /// Synchronizace offline queue
  Future<void> syncOfflineQueue() async {
    if (_offlineQueue.isEmpty) return;

    final toSync = List<Map<String, dynamic>>.from(_offlineQueue);
    _offlineQueue.clear();

    for (final report in toSync) {
      try {
        await _dio.post('/reports/batch', data: report);
      } catch (e) {
        // Vrátíme zpět do queue při chybě
        _offlineQueue.add(report);
        debugPrint('Chyba při sync offline reportu: $e');
      }
    }
  }
}

/// Crowdsourced příjezd
class CrowdsourcedArrival {
  final String routeNumber;
  final String direction;
  final DateTime estimatedArrival;
  final int confidence; // 0-100%
  final int reportCount;
  final DateTime lastUpdate;

  CrowdsourcedArrival({
    required this.routeNumber,
    required this.direction,
    required this.estimatedArrival,
    required this.confidence,
    required this.reportCount,
    required this.lastUpdate,
  });

  factory CrowdsourcedArrival.fromJson(Map<String, dynamic> json) {
    return CrowdsourcedArrival(
      routeNumber: json['route_number'] ?? '',
      direction: json['direction'] ?? '',
      estimatedArrival: DateTime.parse(json['estimated_arrival']),
      confidence: json['confidence'] ?? 0,
      reportCount: json['report_count'] ?? 0,
      lastUpdate: DateTime.parse(json['last_update']),
    );
  }
}

/// Uživatelský report
class UserReport {
  final String id;
  final ReportType type;
  final String content;
  final String userId;
  final DateTime timestamp;
  final int upvotes;
  final int downvotes;
  final ReportStatus status;

  UserReport({
    required this.id,
    required this.type,
    required this.content,
    required this.userId,
    required this.timestamp,
    required this.upvotes,
    required this.downvotes,
    required this.status,
  });

  factory UserReport.fromJson(Map<String, dynamic> json) {
    return UserReport(
      id: json['id'] ?? '',
      type: ReportType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ReportType.other,
      ),
      content: json['content'] ?? '',
      userId: json['user_id'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      upvotes: json['upvotes'] ?? 0,
      downvotes: json['downvotes'] ?? 0,
      status: ReportStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ReportStatus.pending,
      ),
    );
  }
}

/// Statistiky crowdsourcingu
class CrowdsourcingStats {
  final int totalReports;
  final int activeUsers;
  final int verifiedStops;
  final double averageAccuracy;
  final Map<String, int> reportsByType;

  CrowdsourcingStats({
    required this.totalReports,
    required this.activeUsers,
    required this.verifiedStops,
    required this.averageAccuracy,
    required this.reportsByType,
  });

  factory CrowdsourcingStats.fromJson(Map<String, dynamic> json) {
    return CrowdsourcingStats(
      totalReports: json['total_reports'] ?? 0,
      activeUsers: json['active_users'] ?? 0,
      verifiedStops: json['verified_stops'] ?? 0,
      averageAccuracy: json['average_accuracy']?.toDouble() ?? 0.0,
      reportsByType: Map<String, int>.from(json['reports_by_type'] ?? {}),
    );
  }
}

/// Uživatelské body
class UserPoints {
  final String userId;
  final int totalPoints;
  final int level;
  final List<Achievement> achievements;
  final int ranking;

  UserPoints({
    required this.userId,
    required this.totalPoints,
    required this.level,
    required this.achievements,
    required this.ranking,
  });

  factory UserPoints.fromJson(Map<String, dynamic> json) {
    return UserPoints(
      userId: json['user_id'] ?? '',
      totalPoints: json['total_points'] ?? 0,
      level: json['level'] ?? 1,
      achievements:
          (json['achievements'] as List?)
              ?.map((a) => Achievement.fromJson(a))
              .toList() ??
          [],
      ranking: json['ranking'] ?? 0,
    );
  }
}

/// Úspěch/Achievement
class Achievement {
  final String id;
  final String name;
  final String description;
  final String icon;
  final DateTime unlockedAt;

  Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.unlockedAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'] ?? '🏆',
      unlockedAt: DateTime.parse(json['unlocked_at']),
    );
  }
}

/// Enums
enum ReportType { delay, occupancy, newStop, routeChange, other }

enum ReportStatus { pending, verified, rejected, spam }

enum DataType { arrival, stop, route, schedule }
