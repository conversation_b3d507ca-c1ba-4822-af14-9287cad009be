import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/transport_simple.dart';
import 'ai_transport_scraper.dart';
import 'enhanced_transport_service.dart';

/// Hybridní transport služba kombinující AI scraping s API fallback
class HybridTransportService {
  static final HybridTransportService _instance = HybridTransportService._internal();
  factory HybridTransportService() => _instance;
  HybridTransportService._internal();

  final AITransportScraper _aiScraper = AITransportScraper();
  final EnhancedTransportService _apiService = EnhancedTransportService();
  
  bool _isInitialized = false;
  final Map<String, DataSource> _preferredSources = {};

  /// Inicializace hybridní služby
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Inicializace obou služeb paralelně
      await Future.wait([
        _aiScraper.initialize(),
        _apiService.initialize(),
      ]);

      // Detekce nejlepš<PERSON>ch zdrojů dat pro každ<PERSON> město
      await _detectBestDataSources();
      
      _isInitialized = true;
      debugPrint('✅ Hybridní transport služba inicializována');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci hybridní služby: $e');
      _isInitialized = true; // Pokračujeme i s chybou
    }
  }

  /// Získání zastávek s automatickým výběrem nejlepšího zdroje
  Future<List<TransportStop>> getStopsForCity(String cityId) async {
    await _ensureInitialized();

    final source = _preferredSources[cityId] ?? DataSource.aiScraping;
    
    try {
      switch (source) {
        case DataSource.api:
          debugPrint('🔗 Používám API pro zastávky v $cityId');
          return await _apiService.getStopsForCity(cityId);
          
        case DataSource.aiScraping:
          debugPrint('🤖 Používám AI scraping pro zastávky v $cityId');
          final stops = await _aiScraper.getStopsForCity(cityId);
          
          // Pokud AI scraping selže, zkusíme API
          if (stops.isEmpty) {
            debugPrint('🔄 AI scraping neúspěšný, zkouším API...');
            return await _apiService.getStopsForCity(cityId);
          }
          
          return stops;
          
        case DataSource.hybrid:
          debugPrint('🔀 Používám hybridní přístup pro zastávky v $cityId');
          return await _getStopsHybrid(cityId);
      }
    } catch (e) {
      debugPrint('❌ Chyba při získávání zastávek: $e');
      
      // Fallback na druhý zdroj
      if (source == DataSource.api) {
        return await _aiScraper.getStopsForCity(cityId);
      } else {
        return await _apiService.getStopsForCity(cityId);
      }
    }
  }

  /// Získání real-time dat s inteligentním fallback
  Future<List<RealTimeArrival>> getRealTimeArrivals(String stopId, String cityId) async {
    await _ensureInitialized();

    final source = _preferredSources[cityId] ?? DataSource.aiScraping;
    
    try {
      switch (source) {
        case DataSource.api:
          debugPrint('🔗 Používám API pro real-time data');
          return await _apiService.getRealTimeArrivals(stopId, cityId);
          
        case DataSource.aiScraping:
          debugPrint('🤖 Používám AI scraping pro real-time data');
          final arrivals = await _aiScraper.getRealTimeArrivals(stopId, cityId);
          
          // Pokud AI scraping nevrátí real-time data, zkusíme API
          if (arrivals.isEmpty || !arrivals.any((a) => a.isRealTime)) {
            debugPrint('🔄 AI scraping bez real-time dat, zkouším API...');
            return await _apiService.getRealTimeArrivals(stopId, cityId);
          }
          
          return arrivals;
          
        case DataSource.hybrid:
          debugPrint('🔀 Používám hybridní přístup pro real-time data');
          return await _getRealTimeHybrid(stopId, cityId);
      }
    } catch (e) {
      debugPrint('❌ Chyba při získávání real-time dat: $e');
      
      // Fallback strategie
      if (source == DataSource.api) {
        return await _aiScraper.getRealTimeArrivals(stopId, cityId);
      } else {
        return await _apiService.getRealTimeArrivals(stopId, cityId);
      }
    }
  }

  /// Plánování tras s kombinací zdrojů
  Future<List<TransportRoute>> planRoute({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    List<TransportType> allowedTypes = const [
      TransportType.bus,
      TransportType.tram,
      TransportType.train,
    ],
  }) async {
    await _ensureInitialized();

    try {
      // Pro plánování tras preferujeme API službu
      debugPrint('🗺️ Plánuji trasu pomocí API služby');
      final routes = await _apiService.planRoute(
        fromLat: fromLat,
        fromLng: fromLng,
        toLat: toLat,
        toLng: toLng,
        departureTime: departureTime,
        allowedTypes: allowedTypes,
      );

      // Obohacení tras o AI data
      return await _enrichRoutesWithAIData(routes);
    } catch (e) {
      debugPrint('❌ Chyba při plánování tras: $e');
      
      // Fallback na základní trasy
      return await _createBasicRoutes(fromLat, fromLng, toLat, toLng);
    }
  }

  /// Získání dopravních upozornění
  Future<List<TrafficAlert>> getTrafficAlerts({
    String? cityId,
    double? latitude,
    double? longitude,
  }) async {
    await _ensureInitialized();

    try {
      // Kombinujeme upozornění z obou zdrojů
      final apiAlerts = await _apiService.getTrafficAlerts(
        cityId: cityId,
        latitude: latitude,
        longitude: longitude,
      );

      // AI scraping může najít dodatečná upozornění na webech
      final aiAlerts = await _scrapeTrafficAlerts(cityId);

      // Sloučení a deduplikace
      return _mergeTrafficAlerts(apiAlerts, aiAlerts);
    } catch (e) {
      debugPrint('❌ Chyba při získávání dopravních upozornění: $e');
      return [];
    }
  }

  /// Nákup jízdenky (pouze přes API)
  Future<Ticket?> purchaseTicket({
    required String cityId,
    required TicketType type,
    required double price,
    String? routeId,
    String? userId,
  }) async {
    await _ensureInitialized();

    // Jízdenky kupujeme pouze přes API
    return await _apiService.purchaseTicket(
      cityId: cityId,
      type: type,
      price: price,
      routeId: routeId,
      userId: userId,
    );
  }

  /// Detekce nejlepších zdrojů dat pro každé město
  Future<void> _detectBestDataSources() async {
    final cities = ['zagreb', 'split', 'rijeka', 'dubrovnik'];
    
    for (final city in cities) {
      try {
        debugPrint('🔍 Testujem zdroje dat pro $city...');
        
        // Test API dostupnosti
        final apiStops = await _apiService.getStopsForCity(city).timeout(
          const Duration(seconds: 5),
          onTimeout: () => <TransportStop>[],
        );
        
        // Test AI scraping
        final aiStops = await _aiScraper.getStopsForCity(city).timeout(
          const Duration(seconds: 10),
          onTimeout: () => <TransportStop>[],
        );
        
        // Rozhodnutí o nejlepším zdroji
        if (apiStops.isNotEmpty && aiStops.isNotEmpty) {
          _preferredSources[city] = DataSource.hybrid;
          debugPrint('✅ $city: Hybridní přístup (API + AI)');
        } else if (apiStops.isNotEmpty) {
          _preferredSources[city] = DataSource.api;
          debugPrint('✅ $city: API preferováno');
        } else if (aiStops.isNotEmpty) {
          _preferredSources[city] = DataSource.aiScraping;
          debugPrint('✅ $city: AI scraping preferováno');
        } else {
          _preferredSources[city] = DataSource.aiScraping; // Default
          debugPrint('⚠️ $city: Žádný zdroj není dostupný, používám AI scraping');
        }
      } catch (e) {
        debugPrint('❌ Chyba při testování zdrojů pro $city: $e');
        _preferredSources[city] = DataSource.aiScraping; // Safe default
      }
    }
  }

  /// Hybridní získání zastávek
  Future<List<TransportStop>> _getStopsHybrid(String cityId) async {
    // Paralelní načtení z obou zdrojů
    final results = await Future.wait([
      _apiService.getStopsForCity(cityId),
      _aiScraper.getStopsForCity(cityId),
    ]);

    final apiStops = results[0];
    final aiStops = results[1];

    // Sloučení a deduplikace
    return _mergeStops(apiStops, aiStops);
  }

  /// Hybridní získání real-time dat
  Future<List<RealTimeArrival>> _getRealTimeHybrid(String stopId, String cityId) async {
    // Preferujeme API pro real-time data
    try {
      final apiArrivals = await _apiService.getRealTimeArrivals(stopId, cityId);
      if (apiArrivals.isNotEmpty) {
        return apiArrivals;
      }
    } catch (e) {
      debugPrint('API real-time selhalo: $e');
    }

    // Fallback na AI scraping
    return await _aiScraper.getRealTimeArrivals(stopId, cityId);
  }

  /// Obohacení tras o AI data
  Future<List<TransportRoute>> _enrichRoutesWithAIData(List<TransportRoute> routes) async {
    // Můžeme přidat dodatečné informace z AI scrapingu
    // Například aktuální zpoždění, výluky, atd.
    return routes;
  }

  /// Vytvoření základních tras jako fallback
  Future<List<TransportRoute>> _createBasicRoutes(
    double fromLat, double fromLng, double toLat, double toLng,
  ) async {
    // Základní trasa jako fallback
    final departure = DateTime.now().add(const Duration(minutes: 5));
    
    return [
      TransportRoute(
        id: 'basic_route_001',
        fromStopId: 'from_basic',
        toStopId: 'to_basic',
        segments: [
          RouteSegment(
            type: SegmentType.walking,
            duration: const Duration(minutes: 5),
            distance: 400,
            instructions: 'Dojděte na nejbližší zastávku',
          ),
          RouteSegment(
            type: SegmentType.bus,
            routeNumber: 'X',
            fromStopName: 'Výchozí zastávka',
            toStopName: 'Cílová zastávka',
            duration: const Duration(minutes: 20),
            distance: 5000,
            price: 4.0,
            instructions: 'Městská doprava',
          ),
        ],
        totalDuration: const Duration(minutes: 25),
        totalDistance: 5400,
        totalPrice: 4.0,
        currency: 'HRK',
        departureTime: departure,
        arrivalTime: departure.add(const Duration(minutes: 25)),
        transfers: 0,
        routeType: RouteType.fastest,
      ),
    ];
  }

  /// Scraping dopravních upozornění
  Future<List<TrafficAlert>> _scrapeTrafficAlerts(String? cityId) async {
    // AI scraping může najít upozornění na městských webech
    // Implementace by hledala na stránkách měst, dopravních společností
    return [];
  }

  /// Sloučení dopravních upozornění
  List<TrafficAlert> _mergeTrafficAlerts(
    List<TrafficAlert> apiAlerts,
    List<TrafficAlert> aiAlerts,
  ) {
    final merged = <TrafficAlert>[];
    final seenIds = <String>{};

    // Přidáme API upozornění
    for (final alert in apiAlerts) {
      if (!seenIds.contains(alert.id)) {
        merged.add(alert);
        seenIds.add(alert.id);
      }
    }

    // Přidáme AI upozornění (bez duplikátů)
    for (final alert in aiAlerts) {
      if (!seenIds.contains(alert.id)) {
        merged.add(alert);
        seenIds.add(alert.id);
      }
    }

    return merged;
  }

  /// Sloučení zastávek z různých zdrojů
  List<TransportStop> _mergeStops(
    List<TransportStop> apiStops,
    List<TransportStop> aiStops,
  ) {
    final merged = <TransportStop>[];
    final seenNames = <String>{};

    // Preferujeme API data
    for (final stop in apiStops) {
      final key = '${stop.name}_${stop.latitude.toStringAsFixed(4)}_${stop.longitude.toStringAsFixed(4)}';
      if (!seenNames.contains(key)) {
        merged.add(stop);
        seenNames.add(key);
      }
    }

    // Přidáme AI data (bez duplikátů)
    for (final stop in aiStops) {
      final key = '${stop.name}_${stop.latitude.toStringAsFixed(4)}_${stop.longitude.toStringAsFixed(4)}';
      if (!seenNames.contains(key)) {
        merged.add(stop);
        seenNames.add(key);
      }
    }

    return merged;
  }

  /// Zajištění inicializace
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Získání statistik o zdrojích dat
  Map<String, dynamic> getDataSourceStats() {
    return {
      'preferred_sources': _preferredSources,
      'is_initialized': _isInitialized,
      'available_cities': _preferredSources.keys.toList(),
    };
  }

  /// Vyčištění cache
  void clearCache() {
    _aiScraper.clearCache();
    _apiService.clearCache();
  }

  /// Dispose
  void dispose() {
    clearCache();
    _apiService.dispose();
  }
}

/// Enum pro typy zdrojů dat
enum DataSource {
  api,        // Oficiální API
  aiScraping, // AI web scraping
  hybrid,     // Kombinace obou
}

/// Rozšíření pro lepší debugging
extension DataSourceExtension on DataSource {
  String get displayName {
    switch (this) {
      case DataSource.api:
        return 'Oficiální API';
      case DataSource.aiScraping:
        return 'AI Web Scraping';
      case DataSource.hybrid:
        return 'Hybridní přístup';
    }
  }

  String get emoji {
    switch (this) {
      case DataSource.api:
        return '🔗';
      case DataSource.aiScraping:
        return '🤖';
      case DataSource.hybrid:
        return '🔀';
    }
  }
}
