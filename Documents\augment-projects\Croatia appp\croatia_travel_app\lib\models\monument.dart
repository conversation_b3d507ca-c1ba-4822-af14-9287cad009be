class MonumentInfo {
  final String id;
  final String name;
  final String description;
  final String historicalInfo;
  final String? audioGuideUrl;
  final List<String> images;
  final double latitude;
  final double longitude;
  final String region;
  final MonumentCategory category;
  final String buildingPeriod;
  final String? architect;
  final List<String> materials;
  final String visitingHours;
  final double ticketPrice;
  final String accessibility;

  const MonumentInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.historicalInfo,
    this.audioGuideUrl,
    required this.images,
    required this.latitude,
    required this.longitude,
    required this.region,
    required this.category,
    required this.buildingPeriod,
    this.architect,
    required this.materials,
    required this.visitingHours,
    required this.ticketPrice,
    required this.accessibility,
  });
}

class MonumentRecognitionResult {
  final bool isRecognized;
  final MonumentInfo? monument;
  final double confidence;
  final DateTime recognitionTime;
  final Map<String, dynamic>? additionalInfo;
  final String? error;

  const MonumentRecognitionResult({
    required this.isRecognized,
    this.monument,
    required this.confidence,
    required this.recognitionTime,
    this.additionalInfo,
    this.error,
  });
}

enum MonumentCategory {
  fortress,
  palace,
  church,
  monastery,
  castle,
  tower,
  bridge,
  naturalSite,
  archaeological,
  museum,
  other,
}

extension MonumentCategoryExtension on MonumentCategory {
  String get displayName {
    switch (this) {
      case MonumentCategory.fortress:
        return 'Pevnost';
      case MonumentCategory.palace:
        return 'Palác';
      case MonumentCategory.church:
        return 'Kostel';
      case MonumentCategory.monastery:
        return 'Klášter';
      case MonumentCategory.castle:
        return 'Hrad';
      case MonumentCategory.tower:
        return 'Věž';
      case MonumentCategory.bridge:
        return 'Most';
      case MonumentCategory.naturalSite:
        return 'Přírodní lokalita';
      case MonumentCategory.archaeological:
        return 'Archeologické naleziště';
      case MonumentCategory.museum:
        return 'Muzeum';
      case MonumentCategory.other:
        return 'Ostatní';
    }
  }

  String get icon {
    switch (this) {
      case MonumentCategory.fortress:
        return '🏰';
      case MonumentCategory.palace:
        return '🏛️';
      case MonumentCategory.church:
        return '⛪';
      case MonumentCategory.monastery:
        return '🏛️';
      case MonumentCategory.castle:
        return '🏰';
      case MonumentCategory.tower:
        return '🗼';
      case MonumentCategory.bridge:
        return '🌉';
      case MonumentCategory.naturalSite:
        return '🏞️';
      case MonumentCategory.archaeological:
        return '🏺';
      case MonumentCategory.museum:
        return '🏛️';
      case MonumentCategory.other:
        return '📍';
    }
  }
}

class ARMonumentInfo {
  final String monumentId;
  final String title;
  final String shortDescription;
  final List<String> keyFacts;
  final String? audioUrl;
  final List<AROverlay> overlays;

  const ARMonumentInfo({
    required this.monumentId,
    required this.title,
    required this.shortDescription,
    required this.keyFacts,
    this.audioUrl,
    required this.overlays,
  });
}

class AROverlay {
  final String id;
  final AROverlayType type;
  final String content;
  final double x;
  final double y;
  final double width;
  final double height;
  final String? imageUrl;
  final String? audioUrl;

  const AROverlay({
    required this.id,
    required this.type,
    required this.content,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.imageUrl,
    this.audioUrl,
  });
}

enum AROverlayType {
  text,
  image,
  audio,
  video,
  model3d,
  hotspot,
}

class MonumentVisit {
  final String id;
  final String monumentId;
  final DateTime visitDate;
  final List<String> photos;
  final String? notes;
  final double? rating;
  final Duration visitDuration;
  final bool wasRecognized;
  final double? recognitionConfidence;

  const MonumentVisit({
    required this.id,
    required this.monumentId,
    required this.visitDate,
    required this.photos,
    this.notes,
    this.rating,
    required this.visitDuration,
    required this.wasRecognized,
    this.recognitionConfidence,
  });
}

class MonumentQuiz {
  final String monumentId;
  final List<QuizQuestion> questions;
  final int totalPoints;

  const MonumentQuiz({
    required this.monumentId,
    required this.questions,
    required this.totalPoints,
  });
}

class QuizQuestion {
  final String id;
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final int points;
  final String explanation;

  const QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.points,
    required this.explanation,
  });
}

class MonumentCollection {
  final String id;
  final String name;
  final String description;
  final List<String> monumentIds;
  final String? badgeIcon;
  final int requiredVisits;
  final List<String> rewards;

  const MonumentCollection({
    required this.id,
    required this.name,
    required this.description,
    required this.monumentIds,
    this.badgeIcon,
    required this.requiredVisits,
    required this.rewards,
  });
}

// Předdefinované kolekce památek
class MonumentCollections {
  static const List<MonumentCollection> collections = [
    MonumentCollection(
      id: 'unesco_sites',
      name: 'UNESCO Světové dědictví',
      description: 'Navštivte všechna místa UNESCO v Chorvatsku',
      monumentIds: [
        'dubrovnik_walls',
        'diocletian_palace',
        'plitvice_lakes',
        'euphrasian_basilica',
        'cathedral_sibenik',
        'stari_grad_plain',
      ],
      badgeIcon: '🏆',
      requiredVisits: 6,
      rewards: ['Certifikát UNESCO Explorer', 'Speciální průvodce'],
    ),
    
    MonumentCollection(
      id: 'roman_heritage',
      name: 'Římské dědictví',
      description: 'Objevte římskou historii Chorvatska',
      monumentIds: [
        'diocletian_palace',
        'pula_arena',
        'salona_ruins',
      ],
      badgeIcon: '🏛️',
      requiredVisits: 3,
      rewards: ['Římský průvodce', 'Historická mapa'],
    ),
    
    MonumentCollection(
      id: 'medieval_fortresses',
      name: 'Středověké pevnosti',
      description: 'Prozkoumejte hradby a pevnosti',
      monumentIds: [
        'dubrovnik_walls',
        'korcula_walls',
        'trogir_fortress',
      ],
      badgeIcon: '🏰',
      requiredVisits: 3,
      rewards: ['Rytířský certifikát', 'Středověký průvodce'],
    ),
  ];
}
