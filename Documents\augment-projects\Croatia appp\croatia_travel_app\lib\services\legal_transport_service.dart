import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/transport_simple.dart';
import 'open_data_service.dart';
import 'crowdsourcing_service.dart';
import 'partner_services.dart';

/// 100% legální transport služba kombinující všechny legální zdroje
class LegalTransportService {
  static final LegalTransportService _instance =
      LegalTransportService._internal();
  factory LegalTransportService() => _instance;
  LegalTransportService._internal();

  final OpenDataService _openData = OpenDataService();
  final CrowdsourcingService _crowdsourcing = CrowdsourcingService();
  final PartnerServices _partners = PartnerServices();

  bool _isInitialized = false;
  final Map<String, DataQuality> _dataQuality = {};

  /// Inicializace všech legálních služeb
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔒 Inicializuji legální transport služby...');

      // Paralelní inicializace všech služeb
      await Future.wait([
        _openData.initialize(),
        _crowdsourcing.initialize(),
        _partners.initialize(),
      ]);

      // Analýza kvality dat pro každé město
      await _analyzeDataQuality();

      _isInitialized = true;
      debugPrint('✅ Legální transport služby inicializovány');
    } catch (e) {
      debugPrint('❌ Chyba při inicializaci legálních služeb: $e');
      _isInitialized = true; // Pokračujeme i s chybou
    }
  }

  /// Získání zastávek pomocí legálních zdrojů
  Future<List<TransportStop>> getStopsForCity(String cityId) async {
    await _ensureInitialized();

    try {
      debugPrint('🏛️ Získávám zastávky pro $cityId z legálních zdrojů...');

      final allStops = <TransportStop>[];
      final quality = _dataQuality[cityId] ?? DataQuality.unknown;

      // 1. Open Data (nejvyšší priorita)
      if (quality.hasOpenData) {
        debugPrint('📊 Načítám Open Data pro $cityId...');
        final openDataStops = await _openData.getStopsFromOpenData(cityId);
        allStops.addAll(openDataStops);
        debugPrint('✅ Open Data: ${openDataStops.length} zastávek');
      }

      // 2. OpenStreetMap (community data)
      debugPrint('🗺️ Načítám OSM data pro $cityId...');
      final coords = _getCityCoordinates(cityId);
      final osmStops = await _partners.getOSMTransportStops(
        latitude: coords.latitude,
        longitude: coords.longitude,
        radiusKm: 10.0, // Větší radius pro města
      );
      allStops.addAll(osmStops);
      debugPrint('✅ OSM: ${osmStops.length} zastávek');

      // 3. Partner API (pokud dostupné)
      if (quality.hasPartnerAPI) {
        debugPrint('🤝 Načítám partner data pro $cityId...');
        final partnerStops = await _partners.getBestAvailableStops(
          latitude: coords.latitude,
          longitude: coords.longitude,
          radiusKm: 5.0,
        );
        allStops.addAll(partnerStops);
        debugPrint('✅ Partner API: ${partnerStops.length} zastávek');
      }

      // 4. Deduplikace a merge
      final mergedStops = _mergeAndDeduplicateStops(allStops);
      debugPrint('🔄 Po deduplikaci: ${mergedStops.length} zastávek');

      return mergedStops;
    } catch (e) {
      debugPrint('❌ Chyba při získávání zastávek: $e');
      return _getFallbackStops(cityId);
    }
  }

  /// Získání real-time dat pomocí crowdsourcingu
  Future<List<RealTimeArrival>> getRealTimeArrivals(
    String stopId,
    String cityId,
  ) async {
    await _ensureInitialized();

    try {
      debugPrint('🚌 Získávám real-time data pro $stopId...');

      // Crowdsourcing je náš hlavní zdroj real-time dat
      final crowdsourcedArrivals = await _crowdsourcing.getCrowdsourcedArrivals(
        stopId,
      );

      if (crowdsourcedArrivals.isNotEmpty) {
        debugPrint('✅ Crowdsourcing: ${crowdsourcedArrivals.length} příjezdů');
        return _convertCrowdsourcedArrivals(crowdsourcedArrivals);
      }

      // Fallback na mock data s upozorněním
      debugPrint('⚠️ Žádná crowdsourced data, používám fallback');
      return _getFallbackRealTimeArrivals(stopId);
    } catch (e) {
      debugPrint('❌ Chyba při získávání real-time dat: $e');
      return _getFallbackRealTimeArrivals(stopId);
    }
  }

  /// Získání tras pomocí kombinace zdrojů
  Future<List<PublicTransport>> getRoutesForCity(String cityId) async {
    await _ensureInitialized();

    try {
      debugPrint('🚌 Získávám trasy pro $cityId...');

      final allRoutes = <PublicTransport>[];
      final quality = _dataQuality[cityId] ?? DataQuality.unknown;

      // 1. Open Data trasy
      if (quality.hasOpenData) {
        final openDataRoutes = await _openData.getRoutesFromOpenData(cityId);
        allRoutes.addAll(openDataRoutes);
        debugPrint('✅ Open Data trasy: ${openDataRoutes.length}');
      }

      // 2. Pokud nemáme dostatek dat, použijeme fallback
      if (allRoutes.isEmpty) {
        debugPrint('⚠️ Žádné Open Data trasy, používám fallback');
        allRoutes.addAll(_getFallbackRoutes(cityId));
      }

      return allRoutes;
    } catch (e) {
      debugPrint('❌ Chyba při získávání tras: $e');
      return _getFallbackRoutes(cityId);
    }
  }

  /// Plánování tras pomocí legálních zdrojů
  Future<List<TransportRoute>> planRoute({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    List<TransportType> allowedTypes = const [
      TransportType.bus,
      TransportType.tram,
      TransportType.train,
    ],
  }) async {
    await _ensureInitialized();

    try {
      debugPrint('🗺️ Plánuji trasu pomocí legálních zdrojů...');

      final routes = <TransportRoute>[];

      // 1. Google Transit (pokud máme API klíč)
      try {
        final googleRoutes = await _partners.getGoogleTransitRoutes(
          fromLat: fromLat,
          fromLng: fromLng,
          toLat: toLat,
          toLng: toLng,
          departureTime: departureTime,
        );
        routes.addAll(googleRoutes);
        debugPrint('✅ Google Transit: ${googleRoutes.length} tras');
      } catch (e) {
        debugPrint('⚠️ Google Transit nedostupný: $e');
      }

      // 2. Pokud nemáme Google data, vytvoříme základní trasu
      if (routes.isEmpty) {
        debugPrint('🔄 Vytvářím základní trasu...');
        routes.addAll(await _createBasicRoute(fromLat, fromLng, toLat, toLng));
      }

      return routes;
    } catch (e) {
      debugPrint('❌ Chyba při plánování trasy: $e');
      return _getFallbackTransportRoutes(fromLat, fromLng, toLat, toLng);
    }
  }

  /// Reportování dat od uživatelů (crowdsourcing)
  Future<bool> reportUserData({
    required UserReportType type,
    required Map<String, dynamic> data,
    required String userId,
  }) async {
    await _ensureInitialized();

    try {
      switch (type) {
        case UserReportType.delay:
          return await _crowdsourcing.reportDelay(
            stopId: data['stopId'],
            routeNumber: data['routeNumber'],
            delay: data['delay'],
            userId: userId,
            comment: data['comment'],
          );

        case UserReportType.occupancy:
          return await _crowdsourcing.reportOccupancy(
            routeNumber: data['routeNumber'],
            vehicleId: data['vehicleId'],
            occupancy: data['occupancy'],
            userId: userId,
          );

        case UserReportType.newStop:
          return await _crowdsourcing.submitNewStop(
            name: data['name'],
            latitude: data['latitude'],
            longitude: data['longitude'],
            city: data['city'],
            userId: userId,
            routes: data['routes'] ?? [],
            description: data['description'],
          );
      }
    } catch (e) {
      debugPrint('❌ Chyba při reportování: $e');
      return false;
    }
  }

  /// Získání statistik crowdsourcingu
  Future<CrowdsourcingStats?> getCrowdsourcingStats() async {
    await _ensureInitialized();
    return await _crowdsourcing.getStats();
  }

  /// Analýza kvality dat pro města
  Future<void> _analyzeDataQuality() async {
    final cities = ['zagreb', 'split', 'rijeka', 'dubrovnik'];

    for (final city in cities) {
      try {
        debugPrint('🔍 Analyzuji kvalitu dat pro $city...');

        // Test Open Data dostupnosti
        final openDataStops = await _openData
            .getStopsFromOpenData(city)
            .timeout(
              const Duration(seconds: 10),
              onTimeout: () => <TransportStop>[],
            );

        // Test OSM dat
        final coords = _getCityCoordinates(city);
        final osmStops = await _partners
            .getOSMTransportStops(
              latitude: coords.latitude,
              longitude: coords.longitude,
              radiusKm: 5.0,
            )
            .timeout(
              const Duration(seconds: 10),
              onTimeout: () => <TransportStop>[],
            );

        _dataQuality[city] = DataQuality(
          hasOpenData: openDataStops.isNotEmpty,
          hasOSMData: osmStops.isNotEmpty,
          hasPartnerAPI: false, // Bude aktualizováno při testování
          openDataStops: openDataStops.length,
          osmStops: osmStops.length,
          lastAnalyzed: DateTime.now(),
        );

        debugPrint(
          '✅ $city: Open Data: ${openDataStops.length}, OSM: ${osmStops.length}',
        );
      } catch (e) {
        debugPrint('❌ Chyba při analýze $city: $e');
        _dataQuality[city] = DataQuality.unknown;
      }
    }
  }

  /// Sloučení a deduplikace zastávek
  List<TransportStop> _mergeAndDeduplicateStops(List<TransportStop> stops) {
    final merged = <TransportStop>[];
    final seenLocations = <String>{};

    for (final stop in stops) {
      // Vytvoříme klíč na základě polohy (zaokrouhleno na 4 desetinná místa)
      final locationKey =
          '${stop.latitude.toStringAsFixed(4)}_${stop.longitude.toStringAsFixed(4)}';

      if (!seenLocations.contains(locationKey)) {
        merged.add(stop);
        seenLocations.add(locationKey);
      } else {
        // Pokud už máme zastávku na této pozici, zkusíme sloučit informace
        final existingIndex = merged.indexWhere((existing) {
          final existingKey =
              '${existing.latitude.toStringAsFixed(4)}_${existing.longitude.toStringAsFixed(4)}';
          return existingKey == locationKey;
        });

        if (existingIndex != -1) {
          merged[existingIndex] = _mergeStopInfo(merged[existingIndex], stop);
        }
      }
    }

    return merged;
  }

  /// Sloučení informací o zastávce
  TransportStop _mergeStopInfo(TransportStop existing, TransportStop newStop) {
    return TransportStop(
      id: existing.id, // Zachováme původní ID
      name: existing.name.isNotEmpty ? existing.name : newStop.name,
      latitude: existing.latitude,
      longitude: existing.longitude,
      city: existing.city.isNotEmpty ? existing.city : newStop.city,
      zone: existing.zone ?? newStop.zone,
      platforms: {...existing.platforms, ...newStop.platforms}.toList(),
      facilities: {...existing.facilities, ...newStop.facilities}.toList(),
      isAccessible: existing.isAccessible || newStop.isAccessible,
      hasRealTimeInfo: existing.hasRealTimeInfo || newStop.hasRealTimeInfo,
    );
  }

  /// Konverze crowdsourced dat na RealTimeArrival
  List<RealTimeArrival> _convertCrowdsourcedArrivals(
    List<CrowdsourcedArrival> crowdsourced,
  ) {
    return crowdsourced
        .map(
          (arrival) => RealTimeArrival(
            routeNumber: arrival.routeNumber,
            direction: arrival.direction,
            scheduledTime: arrival.estimatedArrival,
            estimatedTime: arrival.estimatedArrival,
            isRealTime:
                arrival.confidence >
                70, // Považujeme za real-time pokud je confidence > 70%
          ),
        )
        .toList();
  }

  /// Získání souřadnic města
  CityCoordinates _getCityCoordinates(String cityId) {
    switch (cityId.toLowerCase()) {
      case 'zagreb':
        return CityCoordinates(45.8150, 15.9819);
      case 'split':
        return CityCoordinates(43.5081, 16.4402);
      case 'rijeka':
        return CityCoordinates(45.3271, 14.4422);
      case 'dubrovnik':
        return CityCoordinates(42.6507, 18.0944);
      default:
        return CityCoordinates(45.8150, 15.9819); // Default Zagreb
    }
  }

  /// Vytvoření základní trasy
  Future<List<TransportRoute>> _createBasicRoute(
    double fromLat,
    double fromLng,
    double toLat,
    double toLng,
  ) async {
    // Najdeme nejbližší zastávky
    final fromStops = await _partners.getOSMTransportStops(
      latitude: fromLat,
      longitude: fromLng,
      radiusKm: 0.5,
    );

    final toStops = await _partners.getOSMTransportStops(
      latitude: toLat,
      longitude: toLng,
      radiusKm: 0.5,
    );

    if (fromStops.isNotEmpty && toStops.isNotEmpty) {
      final departure = DateTime.now().add(const Duration(minutes: 5));

      return [
        TransportRoute(
          id: 'legal_route_${DateTime.now().millisecondsSinceEpoch}',
          fromStopId: fromStops.first.id,
          toStopId: toStops.first.id,
          segments: [
            RouteSegment(
              type: SegmentType.walking,
              duration: const Duration(minutes: 5),
              distance: 400,
              instructions: 'Dojděte na zastávku ${fromStops.first.name}',
            ),
            RouteSegment(
              type: SegmentType.bus,
              routeNumber: 'X',
              fromStopName: fromStops.first.name,
              toStopName: toStops.first.name,
              duration: const Duration(minutes: 15),
              distance: 3000,
              price: 4.0,
              instructions: 'Městská doprava',
            ),
          ],
          totalDuration: const Duration(minutes: 20),
          totalDistance: 3400,
          totalPrice: 4.0,
          currency: 'HRK',
          departureTime: departure,
          arrivalTime: departure.add(const Duration(minutes: 20)),
          transfers: 0,
          routeType: RouteType.fastest,
        ),
      ];
    }

    return [];
  }

  // Fallback metody
  List<TransportStop> _getFallbackStops(String cityId) {
    return [
      TransportStop(
        id: 'fallback_${cityId}_001',
        name: 'Hlavní nádraží',
        latitude: _getCityCoordinates(cityId).latitude,
        longitude: _getCityCoordinates(cityId).longitude,
        city: cityId,
        platforms: [],
        facilities: [],
        isAccessible: true,
      ),
    ];
  }

  List<RealTimeArrival> _getFallbackRealTimeArrivals(String stopId) {
    final now = DateTime.now();
    return [
      RealTimeArrival(
        routeNumber: 'X',
        direction: 'Centrum',
        scheduledTime: now.add(const Duration(minutes: 5)),
        estimatedTime: now.add(const Duration(minutes: 5)),
        isRealTime: false,
      ),
    ];
  }

  List<PublicTransport> _getFallbackRoutes(String cityId) {
    return [
      PublicTransport(
        id: 'fallback_route_$cityId',
        city: cityId,
        type: TransportType.bus,
        routeNumber: 'X',
        routeName: 'Městská linka',
        direction: 'Centrum',
        stops: [],
        operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        startTime: '05:00',
        endTime: '23:00',
        frequency: 15,
        price: 4.0,
        currency: 'HRK',
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  List<TransportRoute> _getFallbackTransportRoutes(
    double fromLat,
    double fromLng,
    double toLat,
    double toLng,
  ) {
    final departure = DateTime.now().add(const Duration(minutes: 5));

    return [
      TransportRoute(
        id: 'fallback_route_001',
        fromStopId: 'fallback_from',
        toStopId: 'fallback_to',
        segments: [
          RouteSegment(
            type: SegmentType.walking,
            duration: const Duration(minutes: 25),
            distance: 2000,
            instructions: 'Pěší trasa',
          ),
        ],
        totalDuration: const Duration(minutes: 25),
        totalDistance: 2000,
        totalPrice: 0.0,
        currency: 'HRK',
        departureTime: departure,
        arrivalTime: departure.add(const Duration(minutes: 25)),
        transfers: 0,
        routeType: RouteType.walking,
      ),
    ];
  }

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Získání informací o kvalitě dat
  Map<String, DataQuality> getDataQualityInfo() => <String, DataQuality>{
    ..._dataQuality,
  };

  /// Vyčištění cache
  void clearCache() {
    _openData.clearCache();
  }

  /// Dispose
  void dispose() {
    clearCache();
  }
}

/// Pomocné třídy
class CityCoordinates {
  final double latitude;
  final double longitude;

  CityCoordinates(this.latitude, this.longitude);
}

class DataQuality {
  final bool hasOpenData;
  final bool hasOSMData;
  final bool hasPartnerAPI;
  final int openDataStops;
  final int osmStops;
  final DateTime lastAnalyzed;

  DataQuality({
    required this.hasOpenData,
    required this.hasOSMData,
    required this.hasPartnerAPI,
    required this.openDataStops,
    required this.osmStops,
    required this.lastAnalyzed,
  });

  static DataQuality get unknown => DataQuality(
    hasOpenData: false,
    hasOSMData: false,
    hasPartnerAPI: false,
    openDataStops: 0,
    osmStops: 0,
    lastAnalyzed: DateTime.now(),
  );
}

enum UserReportType { delay, occupancy, newStop }
