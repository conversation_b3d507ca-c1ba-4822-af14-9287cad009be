import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../widgets/transport_widget.dart';
import '../widgets/real_time_transport_widget.dart';
import '../widgets/ai_scraping_debug_widget.dart';
import '../widgets/legal_data_sources_widget.dart';
import '../widgets/smart_city_widget.dart';
import '../services/hybrid_transport_service.dart';
import '../models/transport_simple.dart';

/// Hlavní obrazovka pro dopravu s real-time funkcemi
class TransportScreen extends StatefulWidget {
  const TransportScreen({super.key});

  @override
  State<TransportScreen> createState() => _TransportScreenState();
}

class _TransportScreenState extends State<TransportScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final HybridTransportService _transportService = HybridTransportService();

  Position? _currentPosition;
  String _selectedCity = 'zagreb';
  bool _isLocationEnabled = false;

  final List<String> _supportedCities = [
    'zagreb',
    'split',
    'rijeka',
    'dubrovnik',
  ];

  final Map<String, String> _cityNames = {
    'zagreb': 'Zagreb',
    'split': 'Split',
    'rijeka': 'Rijeka',
    'dubrovnik': 'Dubrovnik',
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _initializeServices();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _transportService.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      await _transportService.initialize();
    } catch (e) {
      debugPrint('Chyba při inicializaci transport service: $e');
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() => _isLocationEnabled = false);
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() => _isLocationEnabled = false);
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() => _isLocationEnabled = false);
        return;
      }

      final position = await Geolocator.getCurrentPosition();
      setState(() {
        _currentPosition = position;
        _isLocationEnabled = true;
        _selectedCity = _detectCityFromLocation(position);
      });
    } catch (e) {
      debugPrint('Chyba při získávání polohy: $e');
      setState(() => _isLocationEnabled = false);
    }
  }

  String _detectCityFromLocation(Position position) {
    final lat = position.latitude;
    final lng = position.longitude;

    // Zagreb bounds
    if (lat >= 45.75 && lat <= 45.85 && lng >= 15.90 && lng <= 16.05) {
      return 'zagreb';
    }
    // Split bounds
    if (lat >= 43.48 && lat <= 43.55 && lng >= 16.40 && lng <= 16.50) {
      return 'split';
    }
    // Rijeka bounds
    if (lat >= 45.32 && lat <= 45.35 && lng >= 14.40 && lng <= 14.45) {
      return 'rijeka';
    }
    // Dubrovnik bounds
    if (lat >= 42.62 && lat <= 42.68 && lng >= 18.05 && lng <= 18.15) {
      return 'dubrovnik';
    }

    return 'zagreb'; // Default
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Doprava'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          // Výběr města
          PopupMenuButton<String>(
            icon: const Icon(Icons.location_city),
            onSelected: (city) {
              setState(() => _selectedCity = city);
            },
            itemBuilder: (context) => _supportedCities.map((city) {
              return PopupMenuItem<String>(
                value: city,
                child: Row(
                  children: [
                    Icon(
                      city == _selectedCity ? Icons.check : Icons.location_city,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(_cityNames[city] ?? city),
                  ],
                ),
              );
            }).toList(),
          ),
          // Aktualizace polohy
          IconButton(
            icon: Icon(
              _isLocationEnabled ? Icons.gps_fixed : Icons.gps_off,
              color: _isLocationEnabled ? Colors.green : Colors.red,
            ),
            onPressed: _getCurrentLocation,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.live_tv), text: 'Real-time'),
            Tab(icon: Icon(Icons.directions_bus), text: 'Linky'),
            Tab(icon: Icon(Icons.route), text: 'Trasy'),
            Tab(icon: Icon(Icons.confirmation_number), text: 'Jízdenky'),
            Tab(icon: Icon(Icons.location_city), text: 'Chytré město'),
            Tab(icon: Icon(Icons.verified), text: 'Legální'),
            Tab(icon: Icon(Icons.smart_toy), text: 'AI Debug'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Real-time tab
          RealTimeTransportWidget(
            selectedCity: _selectedCity,
            userLatitude: _currentPosition?.latitude,
            userLongitude: _currentPosition?.longitude,
          ),

          // Linky tab
          const TransportWidget(),

          // Trasy tab
          _buildRoutePlannerTab(),

          // Jízdenky tab
          _buildTicketsTab(),

          // Chytré město tab
          const SmartCityWidget(),

          // Legální zdroje tab
          const LegalDataSourcesWidget(),

          // AI Debug tab
          const AiScrapingDebugWidget(),
        ],
      ),
    );
  }

  Widget _buildRoutePlannerTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.route, color: Colors.blue[700]),
                      const SizedBox(width: 8),
                      const Text(
                        'Plánování trasy',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Odkud
                  TextField(
                    decoration: InputDecoration(
                      labelText: 'Odkud',
                      prefixIcon: const Icon(Icons.my_location),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.gps_fixed),
                        onPressed: _isLocationEnabled
                            ? () {
                                // Použít aktuální polohu
                              }
                            : null,
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Kam
                  TextField(
                    decoration: InputDecoration(
                      labelText: 'Kam',
                      prefixIcon: const Icon(Icons.place),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Možnosti
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // Implementace plánování trasy
                            _planRoute();
                          },
                          icon: const Icon(Icons.search),
                          label: const Text('Najít trasu'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () {
                          // Rozšířené možnosti
                          _showAdvancedOptions();
                        },
                        icon: const Icon(Icons.tune),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Rychlé trasy
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Rychlé trasy',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),

                  _buildQuickRoute(
                    'Hlavní nádraží',
                    'Centrum',
                    '6 min',
                    Icons.tram,
                  ),
                  _buildQuickRoute(
                    'Letiště',
                    'Centrum',
                    '25 min',
                    Icons.directions_bus,
                  ),
                  _buildQuickRoute(
                    'Autobusové nádraží',
                    'Centrum',
                    '12 min',
                    Icons.directions_bus,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickRoute(
    String from,
    String to,
    String duration,
    IconData icon,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue[700]),
      title: Text('$from → $to'),
      subtitle: Text(duration),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: () {
        // Implementace rychlé trasy
      },
    );
  }

  Widget _buildTicketsTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.confirmation_number, color: Colors.blue[700]),
                      const SizedBox(width: 8),
                      const Text(
                        'Nákup jízdenek',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'Město: ${_cityNames[_selectedCity]}',
                    style: const TextStyle(fontSize: 16),
                  ),

                  const SizedBox(height: 16),

                  // Typy jízdenek
                  _buildTicketOption(
                    'Jednorázová',
                    '4,00 HRK',
                    TicketType.single,
                  ),
                  _buildTicketOption('Denní', '15,00 HRK', TicketType.daily),
                  _buildTicketOption('Týdenní', '80,00 HRK', TicketType.weekly),
                  _buildTicketOption(
                    'Měsíční',
                    '280,00 HRK',
                    TicketType.monthly,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Moje jízdenky
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Moje jízdenky',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),

                  const Text(
                    'Žádné aktivní jízdenky',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketOption(String name, String price, TicketType type) {
    return ListTile(
      title: Text(name),
      subtitle: Text(price),
      trailing: ElevatedButton(
        onPressed: () => _purchaseTicket(type),
        child: const Text('Koupit'),
      ),
    );
  }

  void _planRoute() {
    // Implementace plánování trasy
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Plánování trasy'),
        content: const Text('Funkce bude implementována v další verzi.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAdvancedOptions() {
    // Zobrazení rozšířených možností
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Rozšířené možnosti',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            CheckboxListTile(
              title: const Text('Autobus'),
              value: true,
              onChanged: (value) {},
            ),
            CheckboxListTile(
              title: const Text('Tramvaj'),
              value: true,
              onChanged: (value) {},
            ),
            CheckboxListTile(
              title: const Text('Vlak'),
              value: false,
              onChanged: (value) {},
            ),
            CheckboxListTile(
              title: const Text('Trajekt'),
              value: false,
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _purchaseTicket(TicketType type) async {
    try {
      final prices = {
        TicketType.single: 4.0,
        TicketType.daily: 15.0,
        TicketType.weekly: 80.0,
        TicketType.monthly: 280.0,
      };

      final ticket = await _transportService.purchaseTicket(
        cityId: _selectedCity,
        type: type,
        price: prices[type] ?? 4.0,
        userId: 'demo_user',
      );

      if (ticket != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Jízdenka zakoupena: ${ticket.id}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při nákupu jízdenky: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
