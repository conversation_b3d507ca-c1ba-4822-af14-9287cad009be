import 'package:flutter/material.dart';
import '../models/place.dart';

class PlaceCard extends StatelessWidget {
  final Place place;
  final VoidCallback? onTap;
  final VoidCallback? onVisitedToggle;
  final bool showDistance;
  final double? distance;

  const PlaceCard({
    super.key,
    required this.place,
    this.onTap,
    this.onVisitedToggle,
    this.showDistance = false,
    this.distance,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Obrázek nebo placeholder
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                gradient: LinearGradient(
                  colors: _getGradientColors(place.type),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: <PERSON><PERSON>(
                children: [
                  // Placeholder pro obrázek
                  Center(
                    child: Icon(
                      _getPlaceIcon(place.type),
                      size: 48,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),

                  // Označení navštíveno
                  if (place.isVisited)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),

                  // Vzdálenost
                  if (showDistance && distance != null)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _formatDistance(distance!),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Informace o místě
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          place.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (place.rating != null)
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.amber[600],
                            ),
                            const SizedBox(width: 2),
                            Text(
                              place.rating!.toStringAsFixed(1),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  Text(
                    place.description,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Tagy a region
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getTypeColor(
                            place.type,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getTypeLabel(place.type),
                          style: TextStyle(
                            fontSize: 10,
                            color: _getTypeColor(place.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getRegionLabel(place.region),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (onVisitedToggle != null)
                        InkWell(
                          onTap: onVisitedToggle,
                          borderRadius: BorderRadius.circular(20),
                          child: Padding(
                            padding: const EdgeInsets.all(4),
                            child: Icon(
                              place.isVisited
                                  ? Icons.bookmark
                                  : Icons.bookmark_border,
                              size: 20,
                              color: place.isVisited
                                  ? Colors.green
                                  : Colors.grey,
                            ),
                          ),
                        ),
                    ],
                  ),

                  // Datum návštěvy
                  if (place.isVisited && place.visitedDate != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        'Navštíveno: ${_formatDate(place.visitedDate!)}',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[500],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Color> _getGradientColors(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return [Colors.brown.shade400, Colors.brown.shade600];
      case PlaceType.beach:
        return [Colors.blue.shade400, Colors.blue.shade600];
      case PlaceType.restaurant:
        return [Colors.orange.shade400, Colors.orange.shade600];
      case PlaceType.hotel:
        return [Colors.purple.shade400, Colors.purple.shade600];
      case PlaceType.museum:
        return [Colors.indigo.shade400, Colors.indigo.shade600];
      case PlaceType.park:
        return [Colors.green.shade400, Colors.green.shade600];
      case PlaceType.church:
        return [Colors.grey.shade400, Colors.grey.shade600];
      case PlaceType.castle:
        return [Colors.red.shade400, Colors.red.shade600];
      case PlaceType.viewpoint:
        return [Colors.teal.shade400, Colors.teal.shade600];
      case PlaceType.other:
        return [Colors.blueGrey.shade400, Colors.blueGrey.shade600];
    }
  }

  IconData _getPlaceIcon(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return Icons.account_balance;
      case PlaceType.beach:
        return Icons.beach_access;
      case PlaceType.restaurant:
        return Icons.restaurant;
      case PlaceType.hotel:
        return Icons.hotel;
      case PlaceType.museum:
        return Icons.museum;
      case PlaceType.park:
        return Icons.park;
      case PlaceType.church:
        return Icons.church;
      case PlaceType.castle:
        return Icons.castle;
      case PlaceType.viewpoint:
        return Icons.landscape;
      case PlaceType.other:
        return Icons.place;
    }
  }

  Color _getTypeColor(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return Colors.brown;
      case PlaceType.beach:
        return Colors.blue;
      case PlaceType.restaurant:
        return Colors.orange;
      case PlaceType.hotel:
        return Colors.purple;
      case PlaceType.museum:
        return Colors.indigo;
      case PlaceType.park:
        return Colors.green;
      case PlaceType.church:
        return Colors.grey;
      case PlaceType.castle:
        return Colors.red;
      case PlaceType.viewpoint:
        return Colors.teal;
      case PlaceType.other:
        return Colors.blueGrey;
    }
  }

  String _getTypeLabel(PlaceType type) {
    switch (type) {
      case PlaceType.monument:
        return 'Památka';
      case PlaceType.beach:
        return 'Pláž';
      case PlaceType.restaurant:
        return 'Restaurace';
      case PlaceType.hotel:
        return 'Hotel';
      case PlaceType.museum:
        return 'Muzeum';
      case PlaceType.park:
        return 'Park';
      case PlaceType.church:
        return 'Kostel';
      case PlaceType.castle:
        return 'Hrad';
      case PlaceType.viewpoint:
        return 'Vyhlídka';
      case PlaceType.other:
        return 'Ostatní';
    }
  }

  String _getRegionLabel(String region) {
    switch (region) {
      case 'istria':
        return 'Istrie';
      case 'dalmatia':
        return 'Dalmácie';
      case 'slavonia':
        return 'Slavonie';
      case 'lika':
        return 'Lika';
      case 'zagreb':
        return 'Zagreb';
      default:
        return region;
    }
  }

  String _formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()} m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)} km';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }
}
