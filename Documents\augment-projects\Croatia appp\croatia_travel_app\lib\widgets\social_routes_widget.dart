import 'package:flutter/material.dart';
import '../models/social_simple.dart';
import '../services/social_service_simple.dart';

class SocialRoutesWidget extends StatefulWidget {
  const SocialRoutesWidget({super.key});

  @override
  State<SocialRoutesWidget> createState() => _SocialRoutesWidgetState();
}

class _SocialRoutesWidgetState extends State<SocialRoutesWidget>
    with TickerProviderStateMixin {
  final SocialService _socialService = SocialService();

  late TabController _tabController;
  List<SharedRoute> _popularRoutes = [];
  List<SharedRoute> _friendsRoutes = [];
  List<SharedRoute> _recommendedRoutes = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final popular = await _socialService.getPopularRoutes();
      final friends = await _socialService.getFriendsRoutes();
      final recommended = await _socialService.getRecommendedRoutes();

      setState(() {
        _popularRoutes = popular;
        _friendsRoutes = friends;
        _recommendedRoutes = recommended;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('Chyba při načítání dat: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildTabBarView(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _shareNewRoute,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sociální trasy',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Objevte a sdílejte nejlepší trasy s komunitou',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: Theme.of(context).primaryColor,
        tabs: const [
          Tab(icon: Icon(Icons.trending_up), text: 'Populární'),
          Tab(icon: Icon(Icons.people), text: 'Přátelé'),
          Tab(icon: Icon(Icons.recommend), text: 'Doporučené'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildRoutesList(_popularRoutes),
        _buildRoutesList(_friendsRoutes),
        _buildRoutesList(_recommendedRoutes),
      ],
    );
  }

  Widget _buildRoutesList(List<SharedRoute> routes) {
    if (routes.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: routes.length,
        itemBuilder: (context, index) {
          return _buildRouteCard(routes[index]);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.route, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'Žádné trasy',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'Buďte první, kdo sdílí trasu!',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildRouteCard(SharedRoute route) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showRouteDetail(route),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRouteHeader(route),
              const SizedBox(height: 12),
              _buildRouteInfo(route),
              const SizedBox(height: 12),
              _buildRouteTags(route),
              const SizedBox(height: 12),
              _buildRouteActions(route),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRouteHeader(SharedRoute route) {
    return Row(
      children: [
        CircleAvatar(
          backgroundImage: route.authorAvatar != null
              ? NetworkImage(route.authorAvatar!)
              : null,
          child: route.authorAvatar == null
              ? Text(route.authorName[0].toUpperCase())
              : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                route.title,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                'od ${route.authorName}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
        Text(
          SocialUtils.formatSocialTime(route.createdAt),
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade500),
        ),
      ],
    );
  }

  Widget _buildRouteInfo(SharedRoute route) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildInfoItem(
              Icons.access_time,
              '${route.route.totalDuration.inMinutes} min',
            ),
          ),
          Expanded(
            child: _buildInfoItem(
              Icons.euro,
              '${route.route.totalPrice.toStringAsFixed(0)} ${route.route.currency}',
            ),
          ),
          Expanded(
            child: _buildInfoItem(
              Icons.transfer_within_a_station,
              '${route.route.transfers} přestupů',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodySmall,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildRouteTags(SharedRoute route) {
    if (route.tags.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: route.tags.take(3).map((tag) {
        return Chip(
          label: Text(tag, style: const TextStyle(fontSize: 12)),
          backgroundColor: Colors.blue.shade50,
          side: BorderSide(color: Colors.blue.shade200),
        );
      }).toList(),
    );
  }

  Widget _buildRouteActions(SharedRoute route) {
    return Row(
      children: [
        _buildActionButton(
          icon: route.isLiked ? Icons.favorite : Icons.favorite_border,
          label: SocialUtils.formatCount(route.likeCount),
          color: route.isLiked ? Colors.red : Colors.grey,
          onTap: () => _toggleLike(route),
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          icon: Icons.star,
          label: route.averageRating > 0
              ? route.averageRating.toStringAsFixed(1)
              : '—',
          color: Colors.orange,
          onTap: () => _showReviews(route),
        ),
        const SizedBox(width: 16),
        _buildActionButton(
          icon: Icons.visibility,
          label: SocialUtils.formatCount(route.viewCount),
          color: Colors.grey,
          onTap: null,
        ),
        const Spacer(),
        IconButton(
          onPressed: () => _showRouteOptions(route),
          icon: const Icon(Icons.more_vert),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(label, style: TextStyle(color: color, fontSize: 12)),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleLike(SharedRoute route) async {
    final success = route.isLiked
        ? await _socialService.unlikeRoute(route.id)
        : await _socialService.likeRoute(route.id);

    if (success) {
      _loadData(); // Refresh data
    } else {
      _showError('Nepodařilo se aktualizovat lajk');
    }
  }

  void _showRouteDetail(SharedRoute route) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => RouteDetailScreen(route: route)),
    );
  }

  void _showReviews(SharedRoute route) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => RouteReviewsScreen(route: route)),
    );
  }

  void _showRouteOptions(SharedRoute route) {
    showModalBottomSheet(
      context: context,
      builder: (context) => _buildRouteOptionsSheet(route),
    );
  }

  Widget _buildRouteOptionsSheet(SharedRoute route) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(
              route.isBookmarked ? Icons.bookmark : Icons.bookmark_border,
            ),
            title: Text(
              route.isBookmarked ? 'Odebrat ze záložek' : 'Přidat do záložek',
            ),
            onTap: () {
              Navigator.pop(context);
              _toggleBookmark(route);
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Sdílet'),
            onTap: () {
              Navigator.pop(context);
              _shareRoute(route);
            },
          ),
          ListTile(
            leading: const Icon(Icons.report),
            title: const Text('Nahlásit'),
            onTap: () {
              Navigator.pop(context);
              _reportRoute(route);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _toggleBookmark(SharedRoute route) async {
    if (route.isBookmarked) {
      // Implementace odebrání ze záložek
    } else {
      final success = await _socialService.bookmarkRoute(route.id);
      if (success) {
        _showSuccess('Přidáno do záložek');
      } else {
        _showError('Nepodařilo se přidat do záložek');
      }
    }
  }

  void _shareRoute(SharedRoute route) {
    // Implementace sdílení trasy
    _showSuccess('Trasa byla sdílena');
  }

  void _reportRoute(SharedRoute route) {
    // Implementace nahlášení trasy
    _showSuccess('Trasa byla nahlášena');
  }

  void _shareNewRoute() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ShareRouteScreen()),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}

// Placeholder screens
class RouteDetailScreen extends StatelessWidget {
  final SharedRoute route;

  const RouteDetailScreen({super.key, required this.route});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(route.title)),
      body: const Center(child: Text('Detail trasy - implementace')),
    );
  }
}

class RouteReviewsScreen extends StatelessWidget {
  final SharedRoute route;

  const RouteReviewsScreen({super.key, required this.route});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Hodnocení')),
      body: const Center(child: Text('Hodnocení tras - implementace')),
    );
  }
}

class ShareRouteScreen extends StatelessWidget {
  const ShareRouteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sdílet trasu')),
      body: const Center(child: Text('Sdílení trasy - implementace')),
    );
  }
}
