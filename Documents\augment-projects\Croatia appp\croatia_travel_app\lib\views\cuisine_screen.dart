import 'package:flutter/material.dart';
import '../models/cuisine.dart';
import '../widgets/cuisine_card.dart';
import '../widgets/recipe_detail_widget.dart';

class CuisineScreen extends StatefulWidget {
  const CuisineScreen({super.key});

  @override
  State<CuisineScreen> createState() => _CuisineScreenState();
}

class _CuisineScreenState extends State<CuisineScreen> with TickerProviderStateMixin {
  List<CuisineItem> _allCuisineItems = [];
  List<CuisineItem> _filteredItems = [];
  String _selectedRegion = 'all';
  CuisineType? _selectedType;
  bool _vegetarianOnly = false;
  bool _veganOnly = false;
  bool _glutenFreeOnly = false;
  String _searchQuery = '';
  
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCuisineData();
  }

  Future<void> _loadCuisineData() async {
    // Simulace načtení dat o chorvatské kuchyni
    _allCuisineItems = [
      CuisineItem(
        id: '1',
        name: 'Peka',
        description: 'Tradiční způsob přípravy masa a zeleniny pod železným poklopem v ohni.',
        region: 'dalmatia',
        type: CuisineType.mainDish,
        ingredients: ['jehněčí maso', 'brambory', 'cibule', 'rozmarýn', 'olivový olej'],
        recipe: 'Maso a zeleninu vložte do pekáče, přikryjte železným poklopem a zapečte v ohni...',
        images: ['peka1.jpg', 'peka2.jpg'],
        restaurants: ['rest1', 'rest2'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        averagePrice: 25.0,
        rating: 4.8,
        tags: ['tradiční', 'Dalmácie', 'maso'],
      ),
      CuisineItem(
        id: '2',
        name: 'Istrijské lanýže',
        description: 'Vzácné lanýže z istrských lesů, používané v různých pokrmech.',
        region: 'istria',
        type: CuisineType.appetizer,
        ingredients: ['bílé lanýže', 'těstoviny', 'parmazán', 'olivový olej'],
        images: ['truffles1.jpg'],
        restaurants: ['rest3', 'rest4'],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        averagePrice: 45.0,
        rating: 4.9,
        tags: ['lanýže', 'Istrie', 'luxusní'],
      ),
      CuisineItem(
        id: '3',
        name: 'Čevapi',
        description: 'Grilované masové válečky podávané s cibulí a lepinjou.',
        region: 'slavonia',
        type: CuisineType.mainDish,
        ingredients: ['mleté maso', 'cibule', 'lepinja', 'ajvar'],
        images: ['cevapi1.jpg'],
        restaurants: ['rest5', 'rest6'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        averagePrice: 8.0,
        rating: 4.5,
        tags: ['gril', 'Slavonie', 'rychlé občerstvení'],
      ),
      CuisineItem(
        id: '4',
        name: 'Fritule',
        description: 'Tradiční chorvatské koblihy s rozinkami a rumem.',
        region: 'all',
        type: CuisineType.dessert,
        ingredients: ['mouka', 'vejce', 'rozinky', 'rum', 'cukr'],
        images: ['fritule1.jpg'],
        restaurants: ['rest7', 'rest8'],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        averagePrice: 5.0,
        rating: 4.3,
        tags: ['dezert', 'tradiční', 'sladké'],
      ),
      CuisineItem(
        id: '5',
        name: 'Riba na gradele',
        description: 'Čerstvá ryba grilovaná na dřevěném uhlí.',
        region: 'dalmatia',
        type: CuisineType.seafood,
        ingredients: ['čerstvá ryba', 'olivový olej', 'česnek', 'petržel', 'citron'],
        images: ['grilled_fish1.jpg'],
        restaurants: ['rest9', 'rest10'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        averagePrice: 20.0,
        rating: 4.7,
        tags: ['ryba', 'gril', 'moře'],
      ),
      CuisineItem(
        id: '6',
        name: 'Maneštra',
        description: 'Hustá polévka se zeleninou a fazolemi, typická pro Istrii.',
        region: 'istria',
        type: CuisineType.soup,
        ingredients: ['fazole', 'zelí', 'brambory', 'kukuřice', 'uzené maso'],
        images: ['manestra1.jpg'],
        restaurants: ['rest11'],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        averagePrice: 12.0,
        rating: 4.4,
        tags: ['polévka', 'Istrie', 'zimní'],
      ),
    ];

    _filteredItems = List.from(_allCuisineItems);
    _filterItems();
    setState(() {});
  }

  void _filterItems() {
    List<CuisineItem> filtered = List.from(_allCuisineItems);

    // Filtrování podle vyhledávání
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        return item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               item.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               item.ingredients.any((ingredient) => 
                   ingredient.toLowerCase().contains(_searchQuery.toLowerCase())) ||
               item.tags.any((tag) => 
                   tag.toLowerCase().contains(_searchQuery.toLowerCase()));
      }).toList();
    }

    // Filtrování podle regionu
    if (_selectedRegion != 'all') {
      filtered = filtered.where((item) => 
          item.region == _selectedRegion || item.region == 'all').toList();
    }

    // Filtrování podle typu
    if (_selectedType != null) {
      filtered = filtered.where((item) => item.type == _selectedType).toList();
    }

    // Dietní filtry
    if (_vegetarianOnly) {
      filtered = filtered.where((item) => item.isVegetarian).toList();
    }
    if (_veganOnly) {
      filtered = filtered.where((item) => item.isVegan).toList();
    }
    if (_glutenFreeOnly) {
      filtered = filtered.where((item) => item.isGlutenFree).toList();
    }

    // Řazení podle hodnocení
    filtered.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));

    setState(() {
      _filteredItems = filtered;
    });
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filtry kuchyně',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Region
            DropdownButtonFormField<String>(
              value: _selectedRegion,
              decoration: const InputDecoration(labelText: 'Region'),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('Celé Chorvatsko')),
                DropdownMenuItem(value: 'istria', child: Text('Istrie')),
                DropdownMenuItem(value: 'dalmatia', child: Text('Dalmácie')),
                DropdownMenuItem(value: 'slavonia', child: Text('Slavonie')),
                DropdownMenuItem(value: 'lika', child: Text('Lika')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedRegion = value!;
                });
                _filterItems();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Typ pokrmu
            DropdownButtonFormField<CuisineType?>(
              value: _selectedType,
              decoration: const InputDecoration(labelText: 'Typ pokrmu'),
              items: const [
                DropdownMenuItem(value: null, child: Text('Všechny typy')),
                DropdownMenuItem(value: CuisineType.mainDish, child: Text('Hlavní chod')),
                DropdownMenuItem(value: CuisineType.appetizer, child: Text('Předkrm')),
                DropdownMenuItem(value: CuisineType.dessert, child: Text('Dezert')),
                DropdownMenuItem(value: CuisineType.soup, child: Text('Polévka')),
                DropdownMenuItem(value: CuisineType.seafood, child: Text('Mořské plody')),
                DropdownMenuItem(value: CuisineType.drink, child: Text('Nápoje')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
                _filterItems();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Dietní omezení
            const Text('Dietní omezení:', style: TextStyle(fontWeight: FontWeight.bold)),
            
            SwitchListTile(
              title: const Text('Vegetariánské'),
              value: _vegetarianOnly,
              onChanged: (value) {
                setState(() {
                  _vegetarianOnly = value;
                  if (value) _veganOnly = false;
                });
                _filterItems();
              },
            ),
            
            SwitchListTile(
              title: const Text('Veganské'),
              value: _veganOnly,
              onChanged: (value) {
                setState(() {
                  _veganOnly = value;
                  if (value) {
                    _vegetarianOnly = false;
                  }
                });
                _filterItems();
              },
            ),
            
            SwitchListTile(
              title: const Text('Bezlepkové'),
              value: _glutenFreeOnly,
              onChanged: (value) {
                setState(() {
                  _glutenFreeOnly = value;
                });
                _filterItems();
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chorvatská kuchyně'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Všechny', icon: Icon(Icons.restaurant)),
            Tab(text: 'Oblíbené', icon: Icon(Icons.favorite)),
            Tab(text: 'Recepty', icon: Icon(Icons.book)),
            Tab(text: 'Restaurace', icon: Icon(Icons.location_on)),
          ],
        ),
      ),
      body: Column(
        children: [
          // Vyhledávací pole
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Vyhledat pokrmy, ingredience...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _filterItems();
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _filterItems();
              },
            ),
          ),

          // Statistiky
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  'Nalezeno: ${_filteredItems.length} pokrmů',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                if (_vegetarianOnly || _veganOnly || _glutenFreeOnly)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _veganOnly ? 'Vegan' : _vegetarianOnly ? 'Vegetarian' : 'Gluten-free',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Obsah podle tabů
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCuisineList(),
                _buildFavoritesList(),
                _buildRecipesList(),
                _buildRestaurantsList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCuisineList() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant_menu,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Žádné pokrmy nenalezeny',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Zkuste změnit filtry nebo vyhledávání',
              style: TextStyle(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CuisineCard(
            cuisineItem: item,
            onTap: () => _showCuisineDetail(item),
          ),
        );
      },
    );
  }

  Widget _buildFavoritesList() {
    final favorites = _filteredItems.where((item) => (item.rating ?? 0) >= 4.5).toList();
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final item = favorites[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CuisineCard(
            cuisineItem: item,
            onTap: () => _showCuisineDetail(item),
          ),
        );
      },
    );
  }

  Widget _buildRecipesList() {
    final withRecipes = _filteredItems.where((item) => item.recipe != null).toList();
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: withRecipes.length,
      itemBuilder: (context, index) {
        final item = withRecipes[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: CuisineCard(
            cuisineItem: item,
            onTap: () => _showRecipeDetail(item),
            showRecipeIcon: true,
          ),
        );
      },
    );
  }

  Widget _buildRestaurantsList() {
    return const Center(
      child: Text(
        'Mapa restaurací\n(Implementováno v MapScreen)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  void _showCuisineDetail(CuisineItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.name,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(item.description),
            const SizedBox(height: 16),
            
            if (item.ingredients.isNotEmpty) ...[
              const Text(
                'Ingredience:',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: item.ingredients.map((ingredient) => Chip(
                  label: Text(ingredient),
                )).toList(),
              ),
              const SizedBox(height: 16),
            ],
            
            if (item.averagePrice != null)
              Text('Průměrná cena: ${item.averagePrice}€'),
            
            if (item.rating != null)
              Row(
                children: [
                  const Icon(Icons.star, color: Colors.amber),
                  Text(' ${item.rating}'),
                ],
              ),
          ],
        ),
      ),
    );
  }

  void _showRecipeDetail(CuisineItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => RecipeDetailWidget(cuisineItem: item),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }
}
