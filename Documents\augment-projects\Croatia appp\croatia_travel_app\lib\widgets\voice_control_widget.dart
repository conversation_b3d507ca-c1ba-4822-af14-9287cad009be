import 'package:flutter/material.dart';
import 'dart:async';
import '../models/voice_command_simple.dart';
import '../services/voice_service.dart';

class VoiceControlWidget extends StatefulWidget {
  final Function(VoiceRecognitionResult)? onResult;
  final bool showFloatingButton;

  const VoiceControlWidget({
    super.key,
    this.onResult,
    this.showFloatingButton = true,
  });

  @override
  State<VoiceControlWidget> createState() => _VoiceControlWidgetState();
}

class _VoiceControlWidgetState extends State<VoiceControlWidget>
    with TickerProviderStateMixin {
  final VoiceService _voiceService = VoiceService();

  bool _isListening = false;
  String _currentStatus = 'Připraveno';
  VoiceRecognitionResult? _lastResult;

  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  // late Animation<double> _waveAnimation; // Unused for now

  StreamSubscription<VoiceRecognitionResult>? _recognitionSubscription;
  StreamSubscription<String>? _statusSubscription;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVoiceService();
    _setupListeners();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _recognitionSubscription?.cancel();
    _statusSubscription?.cancel();
    super.dispose();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // _waveAnimation = Tween<double>(
    //   begin: 0.0,
    //   end: 1.0,
    // ).animate(CurvedAnimation(parent: _waveController, curve: Curves.easeOut));
  }

  Future<void> _initializeVoiceService() async {
    final initialized = await _voiceService.initialize();
    if (!initialized && mounted) {
      _showError('Hlasové ovládání není dostupné');
    }
  }

  void _setupListeners() {
    _recognitionSubscription = _voiceService.recognitionStream.listen((result) {
      setState(() {
        _lastResult = result;
        _isListening = false;
      });

      _stopAnimations();

      if (result.success) {
        _handleSuccessfulCommand(result);
      } else {
        _showError(result.error ?? 'Nerozpoznaný příkaz');
      }

      widget.onResult?.call(result);
    });

    _statusSubscription = _voiceService.statusStream.listen((status) {
      setState(() => _currentStatus = status);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showFloatingButton) {
      return _buildInlineWidget();
    }

    return _buildFloatingButton();
  }

  Widget _buildFloatingButton() {
    return FloatingActionButton(
      onPressed: _toggleListening,
      backgroundColor: _isListening ? Colors.red : Colors.blue,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isListening ? _pulseAnimation.value : 1.0,
            child: Icon(
              _isListening ? Icons.mic : Icons.mic_none,
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }

  Widget _buildInlineWidget() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.record_voice_over, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Hlasové ovládání',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                _buildLanguageSelector(),
              ],
            ),
            const SizedBox(height: 16),

            // Mikrofon tlačítko s animací
            Center(
              child: GestureDetector(
                onTap: _toggleListening,
                child: AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _isListening ? Colors.red : Colors.blue,
                        boxShadow: _isListening
                            ? [
                                BoxShadow(
                                  color: Colors.red.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  spreadRadius: _pulseAnimation.value * 10,
                                ),
                              ]
                            : null,
                      ),
                      child: Transform.scale(
                        scale: _isListening ? _pulseAnimation.value : 1.0,
                        child: Icon(
                          _isListening ? Icons.mic : Icons.mic_none,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Status
            Center(
              child: Text(
                _currentStatus,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _isListening ? Colors.red : Colors.grey.shade600,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Poslední výsledek
            if (_lastResult != null) _buildLastResult(),

            const SizedBox(height: 16),

            // Rychlé příkazy
            _buildQuickCommands(),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.language),
      onSelected: (languageCode) {
        _voiceService.setLanguage(languageCode);
      },
      itemBuilder: (context) {
        return _voiceService.getSupportedLanguages().entries.map((entry) {
          return PopupMenuItem<String>(
            value: entry.key,
            child: Text(entry.value),
          );
        }).toList();
      },
    );
  }

  Widget _buildLastResult() {
    final result = _lastResult!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: result.success
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: result.success
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.success ? Icons.check_circle : Icons.error,
                color: result.success ? Colors.green : Colors.red,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  result.text,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Text(
                VoiceUtils.formatConfidence(result.confidence),
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          if (result.command != null) ...[
            const SizedBox(height: 4),
            Text(
              'Příkaz: ${VoiceUtils.getCommandName(result.command!.type)}',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
            ),
          ],
          if (!result.success && result.error != null) ...[
            const SizedBox(height: 4),
            Text(
              result.error!,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.red),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickCommands() {
    final commands = [
      {'text': 'Najdi cestu do centra', 'icon': Icons.directions},
      {'text': 'Kde je nejbližší restaurace', 'icon': Icons.restaurant},
      {'text': 'Najdi parkování', 'icon': Icons.local_parking},
      {'text': 'Jaké je počasí', 'icon': Icons.wb_sunny},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Rychlé příkazy:',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: commands.map((command) {
            return ActionChip(
              avatar: Icon(command['icon'] as IconData, size: 16),
              label: Text(
                command['text'] as String,
                style: const TextStyle(fontSize: 12),
              ),
              onPressed: () => _executeQuickCommand(command['text'] as String),
            );
          }).toList(),
        ),
      ],
    );
  }

  Future<void> _toggleListening() async {
    if (_isListening) {
      await _stopListening();
    } else {
      await _startListening();
    }
  }

  Future<void> _startListening() async {
    final success = await _voiceService.startListening();
    if (success) {
      setState(() => _isListening = true);
      _startAnimations();
    } else {
      _showError('Nepodařilo se spustit naslouchání');
    }
  }

  Future<void> _stopListening() async {
    await _voiceService.stopListening();
    setState(() => _isListening = false);
    _stopAnimations();
  }

  void _startAnimations() {
    _pulseController.repeat(reverse: true);
    _waveController.repeat();
  }

  void _stopAnimations() {
    _pulseController.stop();
    _waveController.stop();
    _pulseController.reset();
    _waveController.reset();
  }

  void _executeQuickCommand(String command) {
    // Simulace rozpoznání příkazu - toto by mělo být implementováno jinak
    // Pro demo účely vytvoříme mock výsledek
    final result = VoiceRecognitionResult(
      text: command,
      confidence: 1.0,
      success: true,
    );

    setState(() {
      _lastResult = result;
    });

    _handleSuccessfulCommand(result);
    widget.onResult?.call(result);
  }

  void _handleSuccessfulCommand(VoiceRecognitionResult result) {
    // Zde by se provedla akce podle příkazu
    final data = result.data;
    if (data != null) {
      final action = data['action'] as String?;
      switch (action) {
        case 'show_route':
          _showSuccess('Zobrazuji trasu na mapě');
          break;
        case 'show_nearby':
          _showSuccess('Zobrazuji místa v okolí');
          break;
        case 'show_transport':
          _showSuccess('Zobrazuji veřejnou dopravu');
          break;
        case 'show_parking':
          _showSuccess('Zobrazuji parkování');
          break;
        case 'show_weather':
          _showSuccess('Zobrazuji počasí');
          break;
        default:
          _showSuccess('Příkaz byl úspěšně rozpoznán');
      }
    }
  }

  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.green),
      );
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }
}
