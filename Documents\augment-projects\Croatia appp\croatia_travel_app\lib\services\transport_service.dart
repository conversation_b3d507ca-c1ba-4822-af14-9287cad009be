import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/transport.dart';
import '../data/local_database.dart';

class TransportService {
  static final TransportService _instance = TransportService._internal();
  factory TransportService() => _instance;
  TransportService._internal();

  final Dio _dio = Dio();
  final LocalDatabase _localDb = LocalDatabase();

  static const String _baseUrl = 'https://api.croatia-transport.com';
  Timer? _realTimeTimer;

  // ========== VEŘEJNÁ DOPRAVA ==========

  /// Získání všech linek MHD pro město
  Future<List<PublicTransport>> getPublicTransportRoutes(String city) async {
    try {
      final response = await _dio.get('$_baseUrl/transport/routes/$city');
      final List<dynamic> data = response.data['routes'];

      return data.map((json) => _parsePublicTransport(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání linek MHD: $e');
      // Fallback na lokální data
      return await _getLocalTransportRoutes(city);
    }
  }

  /// Získání zastávek v okolí
  Future<List<TransportStop>> getNearbyStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
  }) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/transport/stops/nearby',
        queryParameters: {
          'lat': latitude,
          'lng': longitude,
          'radius': radiusKm,
        },
      );

      final List<dynamic> data = response.data['stops'];
      return data.map((json) => _parseTransportStop(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání zastávek: $e');
      return [];
    }
  }

  /// Real-time informace o příjezdech
  Future<List<RealTimeArrival>> getRealTimeArrivals(String stopId) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/transport/stops/$stopId/arrivals',
      );
      final List<dynamic> data = response.data['arrivals'];

      return data.map((json) => _parseRealTimeArrival(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání real-time dat: $e');
      return [];
    }
  }

  /// Spuštění real-time aktualizací
  void startRealTimeUpdates(
    String stopId,
    Function(List<RealTimeArrival>) onUpdate,
  ) {
    _realTimeTimer?.cancel();
    _realTimeTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      final arrivals = await getRealTimeArrivals(stopId);
      onUpdate(arrivals);
    });
  }

  /// Zastavení real-time aktualizací
  void stopRealTimeUpdates() {
    _realTimeTimer?.cancel();
    _realTimeTimer = null;
  }

  // ========== PLÁNOVÁNÍ TRAS ==========

  /// Plánování trasy veřejnou dopravou
  Future<List<TransportRoute>> planRoute({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    RouteType routeType = RouteType.fastest,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/route/plan',
        data: {
          'from': {'lat': fromLat, 'lng': fromLng},
          'to': {'lat': toLat, 'lng': toLng},
          'departure_time': (departureTime ?? DateTime.now()).toIso8601String(),
          'route_type': routeType.name,
        },
      );

      final List<dynamic> data = response.data['routes'];
      return data.map((json) => _parseTransportRoute(json)).toList();
    } catch (e) {
      debugPrint('Chyba při plánování trasy: $e');
      return [];
    }
  }

  /// Získání detailů trasy
  Future<TransportRoute?> getRouteDetails(String routeId) async {
    try {
      final response = await _dio.get('$_baseUrl/transport/routes/$routeId');
      return _parseTransportRoute(response.data);
    } catch (e) {
      debugPrint('Chyba při načítání detailů trasy: $e');
      return null;
    }
  }

  // ========== JÍZDENKY ==========

  /// Získání dostupných typů jízdenek
  Future<List<TicketType>> getAvailableTicketTypes(String city) async {
    try {
      final response = await _dio.get('$_baseUrl/tickets/types/$city');
      final List<dynamic> data = response.data['types'];

      return data
          .map(
            (type) => TicketType.values.firstWhere(
              (e) => e.name == type,
              orElse: () => TicketType.single,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint('Chyba při načítání typů jízdenek: $e');
      return [TicketType.single, TicketType.daily];
    }
  }

  /// Nákup jízdenky
  Future<Ticket?> purchaseTicket({
    required TicketType type,
    required String city,
    required String paymentMethodId,
    List<String> validRoutes = const [],
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/tickets/purchase',
        data: {
          'type': type.name,
          'city': city,
          'payment_method': paymentMethodId,
          'valid_routes': validRoutes,
        },
      );

      final ticket = _parseTicket(response.data['ticket']);

      // Uložení do lokální databáze
      await _localDb.saveTicket(ticket);

      return ticket;
    } catch (e) {
      debugPrint('Chyba při nákupu jízdenky: $e');
      return null;
    }
  }

  /// Získání aktivních jízdenek uživatele
  Future<List<Ticket>> getUserTickets(String userId) async {
    try {
      // Nejdříve zkusíme lokální databázi
      await _localDb.getUserTickets();

      // Pak synchronizujeme se serverem
      final response = await _dio.get('$_baseUrl/tickets/user/$userId');
      final List<dynamic> data = response.data['tickets'];
      final serverTickets = data.map((json) => _parseTicket(json)).toList();

      // Aktualizujeme lokální databázi
      for (final ticket in serverTickets) {
        await _localDb.saveTicket(ticket);
      }

      return serverTickets;
    } catch (e) {
      debugPrint('Chyba při načítání jízdenek: $e');
      // Fallback na lokální data
      final tickets = await _localDb.getUserTickets();
      return tickets.cast<Ticket>();
    }
  }

  /// Aktivace jízdenky
  Future<bool> activateTicket(String ticketId) async {
    try {
      await _dio.post('$_baseUrl/tickets/$ticketId/activate');

      // Aktualizace v lokální databázi
      await _localDb.updateTicketStatus(ticketId, TicketStatus.active.name);

      return true;
    } catch (e) {
      debugPrint('Chyba při aktivaci jízdenky: $e');
      return false;
    }
  }

  // ========== LOKÁLNÍ DATA ==========

  Future<List<PublicTransport>> _getLocalTransportRoutes(String city) async {
    // Simulace lokálních dat pro offline režim
    return [
      PublicTransport(
        id: 'bus_1',
        city: city,
        type: TransportType.bus,
        routeNumber: '1',
        routeName: 'Hlavní nádraží - Centrum',
        direction: 'Centrum',
        stops: [],
        operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        startTime: '05:00',
        endTime: '23:00',
        frequency: 15,
        price: 12.0,
        currency: 'HRK',
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  /// Vyčištění cache
  Future<void> clearCache() async {
    await _localDb.clearTransportCache();
  }

  // ========== POKROČILÉ FUNKCE ==========

  /// Predikce zpoždění na základě historických dat
  Future<Duration> predictDelay({
    required String routeId,
    required String stopId,
    required DateTime plannedTime,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/predict-delay',
        data: {
          'route_id': routeId,
          'stop_id': stopId,
          'planned_time': plannedTime.toIso8601String(),
          'weather_conditions': await _getWeatherConditions(),
          'traffic_conditions': await _getTrafficConditions(),
        },
      );

      final delayMinutes = response.data['predicted_delay_minutes'] as int;
      return Duration(minutes: delayMinutes);
    } catch (e) {
      debugPrint('Chyba při predikci zpoždění: $e');
      return Duration.zero;
    }
  }

  /// Optimalizace trasy na základě aktuálních podmínek
  Future<List<TransportRoute>> getOptimizedRoutes({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    List<String> avoidRoutes = const [],
    bool includeWalking = true,
    bool includeBiking = false,
    int maxTransfers = 3,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/optimize-route',
        data: {
          'from': {'lat': fromLat, 'lng': fromLng},
          'to': {'lat': toLat, 'lng': toLng},
          'departure_time': (departureTime ?? DateTime.now()).toIso8601String(),
          'avoid_routes': avoidRoutes,
          'include_walking': includeWalking,
          'include_biking': includeBiking,
          'max_transfers': maxTransfers,
          'user_preferences': await _getUserPreferences(),
          'accessibility_needs': await _getAccessibilityNeeds(),
        },
      );

      final List<dynamic> data = response.data['optimized_routes'];
      return data.map((json) => _parseTransportRoute(json)).toList();
    } catch (e) {
      debugPrint('Chyba při optimalizaci trasy: $e');
      return [];
    }
  }

  /// Crowdsourcing informace o obsazenosti vozidel
  Future<void> reportVehicleOccupancy({
    required String vehicleId,
    required String routeId,
    required OccupancyLevel level,
    String? userId,
  }) async {
    try {
      await _dio.post(
        '$_baseUrl/transport/report-occupancy',
        data: {
          'vehicle_id': vehicleId,
          'route_id': routeId,
          'occupancy_level': level.name,
          'timestamp': DateTime.now().toIso8601String(),
          if (userId != null) 'user_id': userId,
        },
      );
    } catch (e) {
      debugPrint('Chyba při hlášení obsazenosti: $e');
    }
  }

  /// Získání informací o obsazenosti vozidel
  Future<Map<String, OccupancyLevel>> getVehicleOccupancy(
    List<String> vehicleIds,
  ) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/occupancy',
        data: {'vehicle_ids': vehicleIds},
      );

      final Map<String, dynamic> data = response.data['occupancy_data'];
      return data.map(
        (key, value) => MapEntry(
          key,
          OccupancyLevel.values.firstWhere(
            (level) => level.name == value,
            orElse: () => OccupancyLevel.unknown,
          ),
        ),
      );
    } catch (e) {
      debugPrint('Chyba při načítání obsazenosti: $e');
      return {};
    }
  }

  /// Inteligentní doporučení tras na základě uživatelského profilu
  Future<List<TransportRoute>> getPersonalizedRoutes({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    String? userId,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/personalized-routes',
        data: {
          'from': {'lat': fromLat, 'lng': fromLng},
          'to': {'lat': toLat, 'lng': toLng},
          if (userId != null) 'user_id': userId,
          'user_history': await _getUserTravelHistory(userId),
          'preferences': await _getUserPreferences(),
          'time_of_day': DateTime.now().hour,
          'day_of_week': DateTime.now().weekday,
        },
      );

      final List<dynamic> data = response.data['personalized_routes'];
      return data.map((json) => _parseTransportRoute(json)).toList();
    } catch (e) {
      debugPrint('Chyba při načítání personalizovaných tras: $e');
      return [];
    }
  }

  /// Multimodální plánování (kombinace různých typů dopravy)
  Future<List<TransportRoute>> planMultimodalRoute({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    List<TransportMode> allowedModes = const [
      TransportMode.walking,
      TransportMode.publicTransport,
      TransportMode.bike,
      TransportMode.car,
    ],
    DateTime? departureTime,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/multimodal-route',
        data: {
          'from': {'lat': fromLat, 'lng': fromLng},
          'to': {'lat': toLat, 'lng': toLng},
          'allowed_modes': allowedModes.map((m) => m.name).toList(),
          'departure_time': (departureTime ?? DateTime.now()).toIso8601String(),
          'carbon_footprint_priority': await _getCarbonFootprintPriority(),
          'cost_priority': await _getCostPriority(),
          'time_priority': await _getTimePriority(),
        },
      );

      final List<dynamic> data = response.data['multimodal_routes'];
      return data.map((json) => _parseTransportRoute(json)).toList();
    } catch (e) {
      debugPrint('Chyba při multimodálním plánování: $e');
      return [];
    }
  }

  // ========== POMOCNÉ METODY ==========

  Future<Map<String, dynamic>> _getWeatherConditions() async {
    // Simulace získání počasí
    return {
      'temperature': 22,
      'precipitation': 0,
      'wind_speed': 5,
      'visibility': 10,
    };
  }

  Future<Map<String, dynamic>> _getTrafficConditions() async {
    // Simulace získání dopravní situace
    return {
      'congestion_level': 'moderate',
      'average_speed': 35,
      'incidents_count': 2,
    };
  }

  Future<Map<String, dynamic>> _getUserPreferences() async {
    // Simulace uživatelských preferencí
    return {
      'prefer_less_walking': false,
      'prefer_air_conditioning': true,
      'prefer_quiet_routes': false,
      'accessibility_needs': [],
    };
  }

  Future<List<String>> _getAccessibilityNeeds() async {
    // Simulace potřeb bezbariérovosti
    return ['wheelchair_accessible', 'audio_announcements'];
  }

  Future<List<Map<String, dynamic>>> _getUserTravelHistory(
    String? userId,
  ) async {
    if (userId == null) return [];

    // Simulace historie cestování
    return [
      {
        'route': 'home_to_work',
        'frequency': 10,
        'preferred_time': '08:00',
        'preferred_routes': ['bus_1', 'tram_3'],
      },
    ];
  }

  Future<double> _getCarbonFootprintPriority() async => 0.3;
  Future<double> _getCostPriority() async => 0.4;
  Future<double> _getTimePriority() async => 0.3;

  /// IDOS-style vyhledávání spojení
  Future<List<TransportRoute>> searchRoutes({
    required String from,
    required String to,
    required DateTime departureTime,
    required bool isDeparture,
  }) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/transport/search-routes',
        data: {
          'from': from,
          'to': to,
          'departure_time': departureTime.toIso8601String(),
          'is_departure': isDeparture,
          'max_results': 5,
          'include_alternatives': true,
        },
      );

      final List<dynamic> data = response.data['routes'];
      return data.map((json) => _parseTransportRoute(json)).toList();
    } catch (e) {
      debugPrint('Chyba při vyhledávání tras: $e');

      // Fallback - simulovaná data pro demo
      return _generateMockRoutes(from, to, departureTime);
    }
  }

  /// Generování mock dat pro demo
  List<TransportRoute> _generateMockRoutes(
    String from,
    String to,
    DateTime departureTime,
  ) {
    return [
      TransportRoute(
        id: 'route_1',
        fromStopId: 'stop_from',
        toStopId: 'stop_to',
        segments: [
          RouteSegment(
            type: SegmentType.walking,
            routeNumber: null,
            fromStopName: from,
            toStopName: 'Zastávka A',
            departureTime: departureTime,
            arrivalTime: departureTime.add(const Duration(minutes: 5)),
            duration: const Duration(minutes: 5),
            distance: 400,
            instructions: 'Dojděte na zastávku',
          ),
          RouteSegment(
            type: SegmentType.bus,
            routeNumber: '14',
            fromStopName: 'Zastávka A',
            toStopName: 'Zastávka B',
            departureTime: departureTime.add(const Duration(minutes: 8)),
            arrivalTime: departureTime.add(const Duration(minutes: 25)),
            duration: const Duration(minutes: 17),
            distance: 8500,
            instructions: 'Autobus 14 směr Centrum',
          ),
          RouteSegment(
            type: SegmentType.walking,
            routeNumber: null,
            fromStopName: 'Zastávka B',
            toStopName: to,
            departureTime: departureTime.add(const Duration(minutes: 25)),
            arrivalTime: departureTime.add(const Duration(minutes: 30)),
            duration: const Duration(minutes: 5),
            distance: 300,
            instructions: 'Dojděte do cíle',
          ),
        ],
        totalDuration: const Duration(minutes: 30),
        totalDistance: 9200,
        totalPrice: 15,
        currency: 'HRK',
        departureTime: departureTime,
        arrivalTime: departureTime.add(const Duration(minutes: 30)),
        transfers: 0,
        routeType: RouteType.fastest,
      ),
      TransportRoute(
        id: 'route_2',
        fromStopId: 'stop_from',
        toStopId: 'stop_to',
        segments: [
          RouteSegment(
            type: SegmentType.walking,
            routeNumber: null,
            fromStopName: from,
            toStopName: 'Metro A',
            departureTime: departureTime,
            arrivalTime: departureTime.add(const Duration(minutes: 8)),
            duration: const Duration(minutes: 8),
            distance: 600,
            instructions: 'Dojděte na metro',
          ),
          RouteSegment(
            type: SegmentType.metro,
            routeNumber: 'M1',
            fromStopName: 'Metro A',
            toStopName: 'Metro B',
            departureTime: departureTime.add(const Duration(minutes: 10)),
            arrivalTime: departureTime.add(const Duration(minutes: 22)),
            duration: const Duration(minutes: 12),
            distance: 7200,
            instructions: 'Metro M1 směr Sever',
          ),
          RouteSegment(
            type: SegmentType.tram,
            routeNumber: '6',
            fromStopName: 'Metro B',
            toStopName: 'Zastávka C',
            departureTime: departureTime.add(const Duration(minutes: 25)),
            arrivalTime: departureTime.add(const Duration(minutes: 35)),
            duration: const Duration(minutes: 10),
            distance: 3200,
            instructions: 'Tramvaj 6 směr Východ',
          ),
          RouteSegment(
            type: SegmentType.walking,
            routeNumber: null,
            fromStopName: 'Zastávka C',
            toStopName: to,
            departureTime: departureTime.add(const Duration(minutes: 35)),
            arrivalTime: departureTime.add(const Duration(minutes: 40)),
            duration: const Duration(minutes: 5),
            distance: 400,
            instructions: 'Dojděte do cíle',
          ),
        ],
        totalDuration: const Duration(minutes: 40),
        totalDistance: 11400,
        totalPrice: 32,
        currency: 'HRK',
        departureTime: departureTime,
        arrivalTime: departureTime.add(const Duration(minutes: 40)),
        transfers: 1,
        routeType: RouteType.leastTransfers,
      ),
    ];
  }

  /// Dispose
  void dispose() {
    _realTimeTimer?.cancel();
  }

  /// Parse PublicTransport from JSON
  PublicTransport _parsePublicTransport(Map<String, dynamic> json) {
    return PublicTransport(
      id: json['id'] ?? '',
      city: json['city'] ?? '',
      type: TransportType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => TransportType.bus,
      ),
      routeNumber: json['route_number'] ?? '',
      routeName: json['route_name'] ?? '',
      direction: json['direction'] ?? '',
      stops: [],
      operatingDays: List<String>.from(json['operating_days'] ?? []),
      startTime: json['start_time'] ?? '05:00',
      endTime: json['end_time'] ?? '23:00',
      frequency: json['frequency'] ?? 15,
      price: (json['price'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'HRK',
      isActive: json['is_active'] ?? true,
      lastUpdated:
          DateTime.tryParse(json['last_updated'] ?? '') ?? DateTime.now(),
    );
  }

  /// Parse TransportStop from JSON
  TransportStop _parseTransportStop(Map<String, dynamic> json) {
    return TransportStop(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      latitude: (json['latitude'] ?? 0).toDouble(),
      longitude: (json['longitude'] ?? 0).toDouble(),
      city: json['city'] ?? '',
      zone: json['zone'] ?? '1',
      platforms: List<String>.from(json['platforms'] ?? ['A']),
      facilities: List<String>.from(json['facilities'] ?? []),
      isAccessible: json['is_accessible'] ?? true,
      hasRealTimeInfo: json['has_real_time_info'] ?? false,
      arrivals: [],
    );
  }

  /// Parse RealTimeArrival from JSON
  RealTimeArrival _parseRealTimeArrival(Map<String, dynamic> json) {
    return RealTimeArrival(
      routeNumber: json['route_number'] ?? '',
      direction: json['direction'] ?? '',
      scheduledTime:
          DateTime.tryParse(json['scheduled_time'] ?? '') ?? DateTime.now(),
      estimatedTime: DateTime.tryParse(json['estimated_time'] ?? ''),
      delay: json['delay_minutes'] != null
          ? Duration(minutes: json['delay_minutes'])
          : null,
      vehicleId: json['vehicle_id'],
      isRealTime: json['is_real_time'] ?? false,
      message: json['message'],
    );
  }

  /// Parse TransportRoute from JSON
  TransportRoute _parseTransportRoute(Map<String, dynamic> json) {
    return TransportRoute(
      id: json['id'] ?? '',
      fromStopId: json['from_stop_id'] ?? '',
      toStopId: json['to_stop_id'] ?? '',
      segments: [],
      totalDuration: Duration(minutes: json['total_duration_minutes'] ?? 30),
      totalDistance: (json['total_distance'] ?? 5000).toDouble(),
      totalPrice: (json['total_price'] ?? 15).toDouble(),
      currency: json['currency'] ?? 'HRK',
      departureTime:
          DateTime.tryParse(json['departure_time'] ?? '') ?? DateTime.now(),
      arrivalTime:
          DateTime.tryParse(json['arrival_time'] ?? '') ??
          DateTime.now().add(const Duration(minutes: 30)),
      transfers: json['transfers'] ?? 0,
      routeType: RouteType.values.firstWhere(
        (t) => t.name == json['route_type'],
        orElse: () => RouteType.fastest,
      ),
    );
  }

  /// Parse Ticket from JSON
  Ticket _parseTicket(Map<String, dynamic> json) {
    return Ticket(
      id: json['id'] ?? '',
      type: TicketType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => TicketType.single,
      ),
      city: json['city'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'HRK',
      purchaseTime:
          DateTime.tryParse(json['purchase_time'] ?? '') ?? DateTime.now(),
      validFrom: DateTime.tryParse(json['valid_from'] ?? '') ?? DateTime.now(),
      validUntil:
          DateTime.tryParse(json['valid_until'] ?? '') ??
          DateTime.now().add(const Duration(hours: 1)),
      status: TicketStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => TicketStatus.active,
      ),
      qrCode: json['qr_code'] ?? '',
    );
  }
}
