import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/transport_simple.dart';

/// Skutečné API integrace pro chorvatské dopravní systémy
class RealTransportApiService {
  static final RealTransportApiService _instance =
      RealTransportApiService._internal();
  factory RealTransportApiService() => _instance;
  RealTransportApiService._internal();

  final Dio _dio = Dio();

  // API klíče a endpointy
  static const String _zagrebetApiKey = 'YOUR_ZAGREBET_API_KEY';
  static const String _splitApiKey = 'YOUR_SPLIT_API_KEY';
  static const String _rijekApiKey = 'YOUR_RIJEKA_API_KEY';

  // Real API endpoints
  static const String _zagrebetBaseUrl = 'https://api.zagrebet.hr/v1';
  static const String _splitBaseUrl = 'https://api.promet-split.hr/v1';
  static const String _rijekaBaseUrl = 'https://api.autotrolej.hr/v1';
  static const String _hzppBaseUrl =
      'https://api.hzpp.hr/v1'; // Hrvatske željeznice
  static const String _jadrolinjaBaseUrl =
      'https://api.jadrolinija.hr/v1'; // Trajekti
  static const String _openStreetMapUrl =
      'https://overpass-api.de/api/interpreter';

  /// Inicializace API služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'CroatiaTravel/1.0',
      },
    );

    // Přidání interceptorů pro logování
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          logPrint: (obj) => debugPrint(obj.toString()),
        ),
      );
    }
  }

  /// Získání aktuálních spojení veřejné dopravy v Záhřebu
  Future<List<PublicTransport>> getZagrebPublicTransport({
    String? fromStopId,
    String? toStopId,
    DateTime? departureTime,
  }) async {
    try {
      final response = await _dio.get(
        '$_zagrebetBaseUrl/routes',
        queryParameters: {
          'api_key': _zagrebetApiKey,
          if (fromStopId != null) 'from': fromStopId,
          if (toStopId != null) 'to': toStopId,
          if (departureTime != null) 'time': departureTime.toIso8601String(),
          'format': 'json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['routes'] ?? [];
        return data.map((json) => _parseZagrebTransport(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání ZET dat: $e');
    }

    // Fallback na mock data
    return _getMockZagrebTransport();
  }

  /// Získání spojení ve Splitu
  Future<List<PublicTransport>> getSplitPublicTransport({
    String? fromStopId,
    String? toStopId,
    DateTime? departureTime,
  }) async {
    try {
      final response = await _dio.get(
        '$_splitBaseUrl/timetable',
        queryParameters: {
          'api_key': _splitApiKey,
          if (fromStopId != null) 'departure_stop': fromStopId,
          if (toStopId != null) 'arrival_stop': toStopId,
          if (departureTime != null)
            'departure_time': departureTime.toIso8601String(),
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['connections'] ?? [];
        return data.map((json) => _parseSplitTransport(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání Split dat: $e');
    }

    return _getMockSplitTransport();
  }

  /// Získání vlakových spojení (Hrvatske željeznice)
  Future<List<TrainConnection>> getTrainConnections({
    required String fromStation,
    required String toStation,
    DateTime? departureTime,
  }) async {
    try {
      final response = await _dio.get(
        '$_hzppBaseUrl/timetable',
        queryParameters: {
          'from': fromStation,
          'to': toStation,
          if (departureTime != null)
            'date': departureTime.toIso8601String().split('T')[0],
          'format': 'json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['trains'] ?? [];
        return data.map((json) => _parseTrainConnection(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání vlakových spojení: $e');
    }

    return _getMockTrainConnections(fromStation, toStation);
  }

  /// Získání trajektových spojení (Jadrolinija)
  Future<List<FerryConnection>> getFerryConnections({
    required String fromPort,
    required String toPort,
    DateTime? departureDate,
  }) async {
    try {
      final response = await _dio.get(
        '$_jadrolinjaBaseUrl/schedule',
        queryParameters: {
          'departure_port': fromPort,
          'arrival_port': toPort,
          if (departureDate != null)
            'date': departureDate.toIso8601String().split('T')[0],
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['ferries'] ?? [];
        return data.map((json) => _parseFerryConnection(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání trajektových spojení: $e');
    }

    return _getMockFerryConnections(fromPort, toPort);
  }

  /// Vyhledání nejbližších zastávek pomocí GPS
  Future<List<TransportStop>> findNearbyStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
    TransportType? transportType,
  }) async {
    try {
      // Použití Overpass API pro OpenStreetMap data
      final query =
          '''
        [out:json][timeout:25];
        (
          node["public_transport"="stop_position"](around:${radiusKm * 1000},$latitude,$longitude);
          node["highway"="bus_stop"](around:${radiusKm * 1000},$latitude,$longitude);
          node["railway"="station"](around:${radiusKm * 1000},$latitude,$longitude);
          node["amenity"="ferry_terminal"](around:${radiusKm * 1000},$latitude,$longitude);
        );
        out body;
      ''';

      final response = await _dio.post(
        _openStreetMapUrl,
        data: query,
        options: Options(headers: {'Content-Type': 'text/plain'}),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final List<dynamic> elements = data['elements'] ?? [];
        return elements.map((json) => _parseOsmStop(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při vyhledávání zastávek: $e');
    }

    return _getMockNearbyStops(latitude, longitude);
  }

  /// Získání real-time informací o dopravě
  Future<List<RealTimeInfo>> getRealTimeInfo({
    required String stopId,
    String? city,
  }) async {
    try {
      String baseUrl;
      String apiKey;

      switch (city?.toLowerCase()) {
        case 'zagreb':
          baseUrl = _zagrebetBaseUrl;
          apiKey = _zagrebetApiKey;
          break;
        case 'split':
          baseUrl = _splitBaseUrl;
          apiKey = _splitApiKey;
          break;
        case 'rijeka':
          baseUrl = _rijekaBaseUrl;
          apiKey = _rijekApiKey;
          break;
        default:
          baseUrl = _zagrebetBaseUrl;
          apiKey = _zagrebetApiKey;
      }

      final response = await _dio.get(
        '$baseUrl/realtime',
        queryParameters: {'stop_id': stopId, 'api_key': apiKey},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['arrivals'] ?? [];
        return data.map((json) => _parseRealTimeInfo(json)).toList();
      }
    } catch (e) {
      debugPrint('Chyba při načítání real-time dat: $e');
    }

    return _getMockRealTimeInfo(stopId);
  }

  /// Vyhledání tras mezi dvěma body
  Future<List<TransportRoute>> findRoutes({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    List<TransportType> allowedTypes = const [
      TransportType.bus,
      TransportType.tram,
      TransportType.train,
      TransportType.ferry,
    ],
  }) async {
    try {
      // Kombinace různých API pro komplexní plánování tras
      final routes = <TransportRoute>[];

      // 1. Vyhledání nejbližších zastávek
      final fromStops = await findNearbyStops(
        latitude: fromLat,
        longitude: fromLng,
        radiusKm: 0.5,
      );

      final toStops = await findNearbyStops(
        latitude: toLat,
        longitude: toLng,
        radiusKm: 0.5,
      );

      // 2. Plánování tras mezi zastávkami
      for (final fromStop in fromStops.take(3)) {
        for (final toStop in toStops.take(3)) {
          final cityRoutes = await _planCityRoute(
            fromStop,
            toStop,
            departureTime,
            allowedTypes,
          );
          routes.addAll(cityRoutes);
        }
      }

      // 3. Seřazení podle kvality
      routes.sort((a, b) => a.totalDuration.compareTo(b.totalDuration));

      return routes.take(5).toList();
    } catch (e) {
      debugPrint('Chyba při plánování tras: $e');
      return _getMockRoutes(fromLat, fromLng, toLat, toLng);
    }
  }

  /// Získání informací o dopravních omezeních
  Future<List<TrafficAlert>> getTrafficAlerts({
    String? city,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      // Kombinace různých zdrojů dopravních informací
      final alerts = <TrafficAlert>[];

      // Zagreb traffic alerts
      if (city == null || city.toLowerCase() == 'zagreb') {
        final zagrebAlerts = await _getZagrebTrafficAlerts();
        alerts.addAll(zagrebAlerts);
      }

      // Split traffic alerts
      if (city == null || city.toLowerCase() == 'split') {
        final splitAlerts = await _getSplitTrafficAlerts();
        alerts.addAll(splitAlerts);
      }

      return alerts;
    } catch (e) {
      debugPrint('Chyba při načítání dopravních upozornění: $e');
      return _getMockTrafficAlerts();
    }
  }

  // ========== PARSING METHODS ==========

  PublicTransport _parseZagrebTransport(Map<String, dynamic> json) {
    return PublicTransport(
      id: json['route_id']?.toString() ?? '',
      city: 'Zagreb',
      type: _parseTransportType(json['vehicle_type']),
      routeNumber: json['route_short_name']?.toString() ?? '',
      routeName: json['route_long_name']?.toString() ?? '',
      direction: json['trip_headsign']?.toString() ?? '',
      stops:
          (json['stops'] as List?)
              ?.map((s) => _parseTransportStop(s))
              .toList() ??
          [],
      operatingDays: (json['service_days'] as List?)?.cast<String>() ?? [],
      startTime: json['start_time']?.toString() ?? '05:00',
      endTime: json['end_time']?.toString() ?? '23:00',
      frequency: json['frequency']?.toInt() ?? 15,
      price: json['fare']?.toDouble() ?? 4.0,
      currency: 'HRK',
      isActive: json['active'] ?? true,
      lastUpdated: DateTime.now(),
    );
  }

  TransportType _parseTransportType(String? type) {
    switch (type?.toLowerCase()) {
      case 'bus':
        return TransportType.bus;
      case 'tram':
        return TransportType.tram;
      case 'train':
        return TransportType.train;
      case 'ferry':
        return TransportType.ferry;
      default:
        return TransportType.bus;
    }
  }

  TransportStop _parseTransportStop(Map<String, dynamic> json) {
    return TransportStop(
      id: json['stop_id']?.toString() ?? '',
      name: json['stop_name']?.toString() ?? '',
      latitude: json['stop_lat']?.toDouble() ?? 0.0,
      longitude: json['stop_lon']?.toDouble() ?? 0.0,
      city: json['city']?.toString() ?? '',
      zone: json['zone']?.toString(),
      platforms: (json['platforms'] as List?)?.cast<String>() ?? [],
      facilities: (json['facilities'] as List?)?.cast<String>() ?? [],
      isAccessible: json['wheelchair_accessible'] ?? false,
    );
  }

  // ========== MOCK DATA METHODS ==========

  List<PublicTransport> _getMockZagrebTransport() {
    return [
      PublicTransport(
        id: 'zet_6',
        city: 'Zagreb',
        type: TransportType.tram,
        routeNumber: '6',
        routeName: 'Črnomerec - Sopot',
        direction: 'Sopot',
        stops: [],
        operatingDays: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
          'sunday',
        ],
        startTime: '04:30',
        endTime: '00:30',
        frequency: 8,
        price: 4.0,
        currency: 'HRK',
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  List<PublicTransport> _getMockSplitTransport() {
    return [
      PublicTransport(
        id: 'split_12',
        city: 'Split',
        type: TransportType.bus,
        routeNumber: '12',
        routeName: 'Centar - Kaštel Stari',
        direction: 'Kaštel Stari',
        stops: [],
        operatingDays: [
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
        ],
        startTime: '05:00',
        endTime: '23:00',
        frequency: 20,
        price: 11.0,
        currency: 'HRK',
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  List<TrainConnection> _getMockTrainConnections(String from, String to) {
    return [
      TrainConnection(
        id: 'hzpp_ic_310',
        trainNumber: 'IC 310',
        fromStation: from,
        toStation: to,
        departureTime: DateTime.now().add(const Duration(hours: 1)),
        arrivalTime: DateTime.now().add(const Duration(hours: 3)),
        duration: const Duration(hours: 2),
        price: 89.0,
        currency: 'HRK',
        trainType: 'InterCity',
        hasReservation: true,
        carriageClasses: ['2. třída', '1. třída'],
        isActive: true,
      ),
    ];
  }

  List<FerryConnection> _getMockFerryConnections(String from, String to) {
    return [
      FerryConnection(
        id: 'jadrolinija_9210',
        lineNumber: '9210',
        shipName: 'Marko Polo',
        fromPort: from,
        toPort: to,
        departureTime: DateTime.now().add(const Duration(hours: 2)),
        arrivalTime: DateTime.now().add(const Duration(hours: 4)),
        duration: const Duration(hours: 2),
        price: 45.0,
        currency: 'HRK',
        vehiclePrice: 180.0,
        hasVehicleDeck: true,
        isActive: true,
      ),
    ];
  }

  List<TransportStop> _getMockNearbyStops(double lat, double lng) {
    return [
      TransportStop(
        id: 'stop_001',
        name: 'Trg bana Jelačića',
        latitude: lat + 0.001,
        longitude: lng + 0.001,
        city: 'Zagreb',
        zone: '1',
        platforms: ['A', 'B'],
        facilities: ['shelter', 'bench', 'display'],
        isAccessible: true,
      ),
    ];
  }

  List<RealTimeInfo> _getMockRealTimeInfo(String stopId) {
    return [
      RealTimeInfo(
        stopId: stopId,
        routeNumber: '6',
        direction: 'Sopot',
        estimatedArrival: DateTime.now().add(const Duration(minutes: 3)),
        delay: const Duration(minutes: 1),
        vehicleId: 'TMK2001',
        isRealTime: true,
      ),
    ];
  }

  List<TransportRoute> _getMockRoutes(
    double fromLat,
    double fromLng,
    double toLat,
    double toLng,
  ) {
    return [
      TransportRoute(
        id: 'route_001',
        fromStopId: 'stop_from',
        toStopId: 'stop_to',
        segments: [],
        totalDuration: const Duration(minutes: 25),
        totalDistance: 5000,
        totalPrice: 4.0,
        currency: 'HRK',
        departureTime: DateTime.now().add(const Duration(minutes: 5)),
        arrivalTime: DateTime.now().add(const Duration(minutes: 30)),
        transfers: 1,
        routeType: RouteType.fastest,
      ),
    ];
  }

  List<TrafficAlert> _getMockTrafficAlerts() {
    return [
      TrafficAlert(
        id: 'alert_001',
        title: 'Dopravní omezení',
        description: 'Uzavírka na Ilici kvůli rekonstrukci',
        severity: AlertSeverity.medium,
        startTime: DateTime.now().subtract(const Duration(hours: 2)),
        endTime: DateTime.now().add(const Duration(days: 7)),
        affectedRoutes: ['6', '11', '12'],
        location: 'Ilica, Zagreb',
        isActive: true,
      ),
    ];
  }

  // ========== HELPER METHODS ==========

  Future<List<TransportRoute>> _planCityRoute(
    TransportStop fromStop,
    TransportStop toStop,
    DateTime? departureTime,
    List<TransportType> allowedTypes,
  ) async {
    // Implementace plánování tras mezi zastávkami
    return [];
  }

  Future<List<TrafficAlert>> _getZagrebTrafficAlerts() async {
    // Implementace pro Zagreb
    return [];
  }

  Future<List<TrafficAlert>> _getSplitTrafficAlerts() async {
    // Implementace pro Split
    return [];
  }

  PublicTransport _parseSplitTransport(Map<String, dynamic> json) {
    // Implementace parsování Split dat
    return _getMockSplitTransport().first;
  }

  TrainConnection _parseTrainConnection(Map<String, dynamic> json) {
    // Implementace parsování vlakových spojení
    return _getMockTrainConnections('', '').first;
  }

  FerryConnection _parseFerryConnection(Map<String, dynamic> json) {
    // Implementace parsování trajektových spojení
    return _getMockFerryConnections('', '').first;
  }

  TransportStop _parseOsmStop(Map<String, dynamic> json) {
    // Implementace parsování OSM dat
    return _getMockNearbyStops(0, 0).first;
  }

  RealTimeInfo _parseRealTimeInfo(Map<String, dynamic> json) {
    // Implementace parsování real-time dat
    return _getMockRealTimeInfo('').first;
  }
}
