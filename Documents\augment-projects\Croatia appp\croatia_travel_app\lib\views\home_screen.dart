import 'package:flutter/material.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isSyncing = false;

  Future<void> _syncData() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Data byla ú<PERSON>ě synchronizována'),
          backgroundColor: Colors.green,
        ),
      );
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _syncData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ADRIATIC DIARY Header s gradientem podle vzoru
              Container(
                width: double.infinity,
                height: 350,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(0xFF2E5984), // Adriatic blue
                      Color(0xFF4A90A4), // Lighter blue
                      Color(0xFFFFB74D), // Warm orange
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    stops: [0.0, 0.6, 1.0],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 20),
                        // Title podle vzoru
                        const Text(
                          'Adriatic Diary',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 1.5,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Explore Croatia with a digital diary',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                        const SizedBox(height: 30),
                        // Phone mockup area podle vzoru
                        Expanded(
                          child: Row(
                            children: [
                              // Calendar section
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'September 2023',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Color(0xFF2E5984),
                                        ),
                                      ),
                                      const SizedBox(height: 12),
                                      // Mini calendar grid podle vzoru
                                      Expanded(
                                        child: GridView.builder(
                                          gridDelegate:
                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                                crossAxisCount: 7,
                                                childAspectRatio: 1,
                                              ),
                                          itemCount: 30,
                                          itemBuilder: (context, index) {
                                            final day = index + 1;
                                            final hasMemory = [
                                              5,
                                              12,
                                              18,
                                              25,
                                            ].contains(day);
                                            return Container(
                                              margin: const EdgeInsets.all(1),
                                              decoration: BoxDecoration(
                                                color: hasMemory
                                                    ? const Color(
                                                        0xFFFFB74D,
                                                      ).withValues(alpha: 0.3)
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  hasMemory
                                                      ? '😊'
                                                      : day.toString(),
                                                  style: TextStyle(
                                                    fontSize: hasMemory
                                                        ? 12
                                                        : 10,
                                                    color: const Color(
                                                      0xFF2E5984,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Map section podle vzoru
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.9),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: const Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Interactive travel calendar',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF666666),
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Map with geotagged memories',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF666666),
                                        ),
                                      ),
                                      SizedBox(height: 16),
                                      Expanded(
                                        child: Center(
                                          child: Icon(
                                            Icons.map,
                                            size: 48,
                                            color: Color(0xFF2E5984),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Rychlé akce
              const Text(
                'Rychlé akce',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuickAction(
                    context,
                    icon: Icons.camera_alt,
                    label: 'Foto',
                    onTap: () => _showFeatureComingSoon(context, 'Fotoaparát'),
                  ),
                  _buildQuickAction(
                    context,
                    icon: Icons.translate,
                    label: 'Slovník',
                    onTap: () => DefaultTabController.of(context).animateTo(7),
                  ),
                  _buildQuickAction(
                    context,
                    icon: Icons.restaurant,
                    label: 'Kuchyně',
                    onTap: () => DefaultTabController.of(context).animateTo(4),
                  ),
                  _buildQuickAction(
                    context,
                    icon: Icons.account_balance_wallet,
                    label: 'Rozpočet',
                    onTap: () => DefaultTabController.of(context).animateTo(5),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Statistiky
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Vaše cesta',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem('Navštíveno', '12', 'míst'),
                          _buildStatItem('Ujetých', '245', 'km'),
                          _buildStatItem('Fotek', '89', 'ks'),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Doporučení
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Doporučení pro vás',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),
                      ListTile(
                        leading: const Icon(Icons.place, color: Colors.blue),
                        title: const Text('Plitvická jezera'),
                        subtitle: const Text(
                          'Národní park s krásnými vodopády',
                        ),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () =>
                            DefaultTabController.of(context).animateTo(2),
                      ),
                      ListTile(
                        leading: const Icon(
                          Icons.restaurant,
                          color: Colors.orange,
                        ),
                        title: const Text('Peka'),
                        subtitle: const Text('Tradiční chorvatský pokrm'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () =>
                            DefaultTabController.of(context).animateTo(4),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 24,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(unit, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }

  void _showFeatureComingSoon(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature bude dostupný v příští verzi'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
