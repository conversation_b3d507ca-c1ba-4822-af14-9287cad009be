import 'package:flutter/material.dart';
import '../widgets/city_payments_widget.dart';
import '../widgets/government_services_widget.dart';
import '../widgets/issue_reporting_widget.dart';
import '../widgets/public_wifi_widget.dart';
import '../widgets/shared_transport_widget.dart';

class SmartCityScreen extends StatefulWidget {
  const SmartCityScreen({super.key});

  @override
  State<SmartCityScreen> createState() => _SmartCityScreenState();
}

class _SmartCityScreenState extends State<SmartCityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Smart City'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.payment), text: 'Platby'),
            Tab(icon: Icon(Icons.account_balance), text: 'Úřady'),
            Tab(icon: Icon(Icons.report_problem), text: 'Problémy'),
            Tab(icon: Icon(Icons.wifi), text: 'WiFi'),
            Tab(icon: Icon(Icons.directions_bike), text: 'Sdílení'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          CityPaymentsWidget(),
          GovernmentServicesWidget(),
          IssueReportingWidget(),
          PublicWifiWidget(),
          SharedTransportWidget(),
        ],
      ),
    );
  }
}
