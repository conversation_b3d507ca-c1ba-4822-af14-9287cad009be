import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// Modely pro rozpočet
class BudgetCategory {
  final String name;
  final double budgeted;
  final double spent;
  final IconData icon;
  final Color color;

  BudgetCategory({
    required this.name,
    required this.budgeted,
    required this.spent,
    required this.icon,
    required this.color,
  });

  double get remaining => budgeted - spent;
  double get percentage => spent / budgeted;
}

class Expense {
  final String title;
  final double amount;
  final String category;
  final DateTime date;
  final String location;

  Expense({
    required this.title,
    required this.amount,
    required this.category,
    required this.date,
    required this.location,
  });
}

class BudgetScreen extends StatefulWidget {
  const BudgetScreen({super.key});

  @override
  State<BudgetScreen> createState() => _BudgetScreenState();
}

class _BudgetScreenState extends State<BudgetScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Mock data
  double _totalBudget = 2000.0;
  double _spent = 847.50;
  List<BudgetCategory> _categories = [];
  List<Expense> _recentExpenses = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadBudgetData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadBudgetData() {
    _categories = [
      BudgetCategory(
        name: 'Ubytování',
        budgeted: 600.0,
        spent: 320.0,
        icon: Icons.hotel,
        color: Colors.blue,
      ),
      BudgetCategory(
        name: 'Jídlo',
        budgeted: 400.0,
        spent: 287.50,
        icon: Icons.restaurant,
        color: Colors.orange,
      ),
      BudgetCategory(
        name: 'Doprava',
        budgeted: 300.0,
        spent: 150.0,
        icon: Icons.directions_car,
        color: Colors.green,
      ),
      BudgetCategory(
        name: 'Aktivity',
        budgeted: 500.0,
        spent: 90.0,
        icon: Icons.local_activity,
        color: Colors.purple,
      ),
      BudgetCategory(
        name: 'Nákupy',
        budgeted: 200.0,
        spent: 0.0,
        icon: Icons.shopping_bag,
        color: Colors.red,
      ),
    ];

    _recentExpenses = [
      Expense(
        title: 'Večeře v restauraci',
        amount: 45.0,
        category: 'Jídlo',
        date: DateTime.now().subtract(const Duration(hours: 2)),
        location: 'Dubrovník',
      ),
      Expense(
        title: 'Vstupné do města',
        amount: 30.0,
        category: 'Aktivity',
        date: DateTime.now().subtract(const Duration(hours: 5)),
        location: 'Dubrovník',
      ),
      Expense(
        title: 'Oběd',
        amount: 25.0,
        category: 'Jídlo',
        date: DateTime.now().subtract(const Duration(days: 1)),
        location: 'Split',
      ),
      Expense(
        title: 'Hotel',
        amount: 80.0,
        category: 'Ubytování',
        date: DateTime.now().subtract(const Duration(days: 1)),
        location: 'Split',
      ),
    ];

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final remaining = _totalBudget - _spent;
    final spentPercentage = _spent / _totalBudget;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rozpočet'),
        actions: [
          IconButton(
            onPressed: _showBudgetSettings,
            icon: const Icon(Icons.settings),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Přehled'),
            Tab(icon: Icon(Icons.category), text: 'Kategorie'),
            Tab(icon: Icon(Icons.receipt), text: 'Výdaje'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(remaining, spentPercentage),
          _buildCategoriesTab(),
          _buildExpensesTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addExpense,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOverviewTab(double remaining, double spentPercentage) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hlavní karta s rozpočtem
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Celkový rozpočet',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                      Text(
                        '${_totalBudget.toStringAsFixed(0)} €',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Kruhový progress
                  SizedBox(
                    height: 120,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          height: 120,
                          width: 120,
                          child: CircularProgressIndicator(
                            value: spentPercentage,
                            strokeWidth: 8,
                            backgroundColor: Colors.grey[200],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              spentPercentage > 0.8
                                  ? Colors.red
                                  : spentPercentage > 0.6
                                  ? Colors.orange
                                  : Colors.green,
                            ),
                          ),
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${_spent.toStringAsFixed(0)} €',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'utraceno',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          Text(
                            '${remaining.toStringAsFixed(0)} €',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: remaining >= 0 ? Colors.green : Colors.red,
                            ),
                          ),
                          const Text(
                            'zbývá',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          Text(
                            '${(spentPercentage * 100).toStringAsFixed(0)}%',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Text(
                            'využito',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Rychlé statistiky
          const Text(
            'Denní průměr',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard('Dnes', '70 €', Icons.today, Colors.blue),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Týden',
                  '65 €',
                  Icons.date_range,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Celkem',
                  '58 €',
                  Icons.trending_up,
                  Colors.orange,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Nejnovější výdaje
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Nejnovější výdaje',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () => _tabController.animateTo(2),
                child: const Text('Zobrazit vše'),
              ),
            ],
          ),

          ..._recentExpenses
              .take(3)
              .map((expense) => _buildExpenseCard(expense)),
        ],
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Kategorie rozpočtu',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return _buildCategoryCard(category);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(BudgetCategory category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: category.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(category.icon, color: category.color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${category.spent.toStringAsFixed(0)} € z ${category.budgeted.toStringAsFixed(0)} €',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${category.remaining.toStringAsFixed(0)} €',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: category.remaining >= 0
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    Text(
                      'zbývá',
                      style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 12),

            LinearProgressIndicator(
              value: category.percentage.clamp(0.0, 1.0),
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                category.percentage > 1.0
                    ? Colors.red
                    : category.percentage > 0.8
                    ? Colors.orange
                    : category.color,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              '${(category.percentage * 100).toStringAsFixed(0)}% využito',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpensesTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Všechny výdaje',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              IconButton(
                onPressed: _showExpenseFilters,
                icon: const Icon(Icons.filter_list),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: _recentExpenses.length,
              itemBuilder: (context, index) {
                final expense = _recentExpenses[index];
                return _buildExpenseCard(expense);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseCard(Expense expense) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getCategoryColor(
            expense.category,
          ).withValues(alpha: 0.1),
          child: Icon(
            _getCategoryIcon(expense.category),
            color: _getCategoryColor(expense.category),
            size: 20,
          ),
        ),
        title: Text(
          expense.title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(expense.category),
            Text(
              '${expense.location} • ${DateFormat('dd.MM.yyyy HH:mm').format(expense.date)}',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
        trailing: Text(
          '${expense.amount.toStringAsFixed(2)} €',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, size: 24, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Ubytování':
        return Colors.blue;
      case 'Jídlo':
        return Colors.orange;
      case 'Doprava':
        return Colors.green;
      case 'Aktivity':
        return Colors.purple;
      case 'Nákupy':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Ubytování':
        return Icons.hotel;
      case 'Jídlo':
        return Icons.restaurant;
      case 'Doprava':
        return Icons.directions_car;
      case 'Aktivity':
        return Icons.local_activity;
      case 'Nákupy':
        return Icons.shopping_bag;
      default:
        return Icons.attach_money;
    }
  }

  void _addExpense() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Přidat výdaj'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Název výdaje',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                labelText: 'Částka (€)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Výdaj bude přidán v příští verzi'),
                ),
              );
            },
            child: const Text('Přidat'),
          ),
        ],
      ),
    );
  }

  void _showBudgetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nastavení rozpočtu'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Celkový rozpočet (€)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            SizedBox(height: 16),
            Text('Upravte kategorie a jejich limity'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Nastavení bude dostupné v příští verzi'),
                ),
              );
            },
            child: const Text('Uložit'),
          ),
        ],
      ),
    );
  }

  void _showExpenseFilters() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrovat výdaje'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Filtry podle kategorie, data a částky'),
            SizedBox(height: 16),
            Text('Bude dostupné v příští verzi'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }
}
