// ========== HLASOVÉ PŘÍKAZY ==========

class VoiceCommand {
  final VoiceCommandType type;
  final String originalText;
  final Map<String, String> parameters;
  final DateTime timestamp;

  VoiceCommand({
    required this.type,
    required this.originalText,
    required this.parameters,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class VoiceRecognitionResult {
  final String text;
  final double confidence;
  final VoiceCommand? command;
  final bool success;
  final String? error;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  VoiceRecognitionResult({
    required this.text,
    required this.confidence,
    this.command,
    required this.success,
    this.error,
    this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

class VoicePattern {
  final VoiceCommandType command;
  final List<String> patterns;
  final double confidence;

  VoicePattern({
    required this.command,
    required this.patterns,
    this.confidence = 1.0,
  });
}

// ========== ENUMS ==========

enum VoiceCommandType {
  findRoute,
  findNearby,
  findTransport,
  findParking,
  weather,
  help,
  navigation,
  search,
  booking,
  emergency,
}

enum VoiceLanguage {
  croatian,
  english,
  german,
  italian,
  czech,
}

enum VoiceRecognitionStatus {
  idle,
  listening,
  processing,
  speaking,
  error,
}

// ========== HLASOVÉ NASTAVENÍ ==========

class VoiceSettings {
  final bool enabled;
  final VoiceLanguage language;
  final double speechRate;
  final double volume;
  final double pitch;
  final bool autoListen;
  final bool confirmCommands;
  final int timeoutSeconds;

  VoiceSettings({
    this.enabled = true,
    this.language = VoiceLanguage.croatian,
    this.speechRate = 0.8,
    this.volume = 0.8,
    this.pitch = 1.0,
    this.autoListen = false,
    this.confirmCommands = true,
    this.timeoutSeconds = 30,
  });

  VoiceSettings copyWith({
    bool? enabled,
    VoiceLanguage? language,
    double? speechRate,
    double? volume,
    double? pitch,
    bool? autoListen,
    bool? confirmCommands,
    int? timeoutSeconds,
  }) {
    return VoiceSettings(
      enabled: enabled ?? this.enabled,
      language: language ?? this.language,
      speechRate: speechRate ?? this.speechRate,
      volume: volume ?? this.volume,
      pitch: pitch ?? this.pitch,
      autoListen: autoListen ?? this.autoListen,
      confirmCommands: confirmCommands ?? this.confirmCommands,
      timeoutSeconds: timeoutSeconds ?? this.timeoutSeconds,
    );
  }
}

// ========== UTILITY CLASSES ==========

class VoiceUtils {
  /// Převod jazyka na locale kód
  static String getLocaleCode(VoiceLanguage language) {
    switch (language) {
      case VoiceLanguage.croatian:
        return 'hr-HR';
      case VoiceLanguage.english:
        return 'en-US';
      case VoiceLanguage.german:
        return 'de-DE';
      case VoiceLanguage.italian:
        return 'it-IT';
      case VoiceLanguage.czech:
        return 'cs-CZ';
    }
  }

  /// Převod locale kódu na jazyk
  static VoiceLanguage getLanguageFromLocale(String locale) {
    switch (locale) {
      case 'hr-HR':
        return VoiceLanguage.croatian;
      case 'en-US':
        return VoiceLanguage.english;
      case 'de-DE':
        return VoiceLanguage.german;
      case 'it-IT':
        return VoiceLanguage.italian;
      case 'cs-CZ':
        return VoiceLanguage.czech;
      default:
        return VoiceLanguage.english;
    }
  }

  /// Získání názvu jazyka
  static String getLanguageName(VoiceLanguage language) {
    switch (language) {
      case VoiceLanguage.croatian:
        return 'Chorvatština';
      case VoiceLanguage.english:
        return 'Angličtina';
      case VoiceLanguage.german:
        return 'Němčina';
      case VoiceLanguage.italian:
        return 'Italština';
      case VoiceLanguage.czech:
        return 'Čeština';
    }
  }

  /// Získání názvu příkazu
  static String getCommandName(VoiceCommandType command) {
    switch (command) {
      case VoiceCommandType.findRoute:
        return 'Najít cestu';
      case VoiceCommandType.findNearby:
        return 'Najít v okolí';
      case VoiceCommandType.findTransport:
        return 'Najít dopravu';
      case VoiceCommandType.findParking:
        return 'Najít parkování';
      case VoiceCommandType.weather:
        return 'Počasí';
      case VoiceCommandType.help:
        return 'Nápověda';
      case VoiceCommandType.navigation:
        return 'Navigace';
      case VoiceCommandType.search:
        return 'Vyhledávání';
      case VoiceCommandType.booking:
        return 'Rezervace';
      case VoiceCommandType.emergency:
        return 'Nouzové volání';
    }
  }

  /// Validace confidence skóre
  static bool isConfidenceAcceptable(double confidence) {
    return confidence >= 0.7;
  }

  /// Formátování confidence jako procenta
  static String formatConfidence(double confidence) {
    return '${(confidence * 100).toStringAsFixed(0)}%';
  }
}
