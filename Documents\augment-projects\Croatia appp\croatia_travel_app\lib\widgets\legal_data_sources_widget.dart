import 'package:flutter/material.dart';
import 'dart:async';
import '../services/legal_transport_service.dart';
import '../services/crowdsourcing_service.dart';

/// Widget pro zobrazení legálních zdro<PERSON> dat a jejich kvality
class LegalDataSourcesWidget extends StatefulWidget {
  const LegalDataSourcesWidget({super.key});

  @override
  State<LegalDataSourcesWidget> createState() => _LegalDataSourcesWidgetState();
}

class _LegalDataSourcesWidgetState extends State<LegalDataSourcesWidget> {
  final LegalTransportService _legalService = LegalTransportService();

  Map<String, DataQuality> _dataQuality = {};
  CrowdsourcingStats? _crowdsourcingStats;
  bool _isLoading = true;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _loadData();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (mounted) {
        _loadData();
      }
    });
  }

  Future<void> _loadData() async {
    try {
      await _legalService.initialize();

      final dataQuality = _legalService.getDataQualityInfo();
      final crowdsourcingStats = await _legalService.getCrowdsourcingStats();

      setState(() {
        _dataQuality = dataQuality;
        _crowdsourcingStats = crowdsourcingStats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Legální zdroje dat'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLegalStatusCard(),
                  const SizedBox(height: 16),
                  _buildDataSourcesCard(),
                  const SizedBox(height: 16),
                  _buildCrowdsourcingCard(),
                  const SizedBox(height: 16),
                  _buildContributeCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildLegalStatusCard() {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.verified, color: Colors.green[700], size: 32),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '100% Legální zdroje dat',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            _buildLegalFeature(
              '🏛️ Oficiální Open Data',
              'Vládní portály a městské databáze',
              Colors.green,
            ),

            _buildLegalFeature(
              '🗺️ OpenStreetMap',
              'Community-driven mapa světa',
              Colors.blue,
            ),

            _buildLegalFeature(
              '🤝 Partnerské API',
              'Google Transit, Moovit, Transitland',
              Colors.orange,
            ),

            _buildLegalFeature(
              '👥 Crowdsourcing',
              'Data od uživatelů aplikace',
              Colors.purple,
            ),

            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[300]!),
              ),
              child: const Row(
                children: [
                  Icon(Icons.security, color: Colors.green),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Žádné porušování Terms of Service • GDPR compliant • Udržitelné řešení',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegalFeature(String title, String description, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataSourcesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.source, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Kvalita dat podle měst',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_dataQuality.isEmpty)
              const Text('Analyzuji kvalitu dat...')
            else
              ..._dataQuality.entries.map((entry) {
                final city = entry.key;
                final quality = entry.value;
                return _buildCityQualityRow(city, quality);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildCityQualityRow(String city, DataQuality quality) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  city.toUpperCase(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              Text(
                'Aktualizováno: ${_formatTime(quality.lastAnalyzed)}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Row(
            children: [
              _buildDataSourceChip(
                'Open Data',
                quality.hasOpenData,
                '${quality.openDataStops} zastávek',
                Colors.green,
              ),
              const SizedBox(width: 8),
              _buildDataSourceChip(
                'OSM',
                quality.hasOSMData,
                '${quality.osmStops} zastávek',
                Colors.blue,
              ),
              const SizedBox(width: 8),
              _buildDataSourceChip(
                'Partner API',
                quality.hasPartnerAPI,
                quality.hasPartnerAPI ? 'Dostupné' : 'Nedostupné',
                Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataSourceChip(
    String label,
    bool available,
    String detail,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: available ? color.withValues(alpha: 0.1) : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: available ? color : Colors.grey[300]!),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  available ? Icons.check_circle : Icons.cancel,
                  size: 16,
                  color: available ? color : Colors.grey,
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: available ? color : Colors.grey,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              detail,
              style: TextStyle(
                fontSize: 10,
                color: available ? color : Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCrowdsourcingCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.people, color: Colors.purple[700]),
                const SizedBox(width: 8),
                const Text(
                  'Crowdsourcing statistiky',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_crowdsourcingStats == null)
              const Text('Načítám statistiky...')
            else
              Column(
                children: [
                  Row(
                    children: [
                      _buildStatCard(
                        'Celkem reportů',
                        '${_crowdsourcingStats!.totalReports}',
                        Icons.report,
                        Colors.blue,
                      ),
                      const SizedBox(width: 12),
                      _buildStatCard(
                        'Aktivní uživatelé',
                        '${_crowdsourcingStats!.activeUsers}',
                        Icons.person,
                        Colors.green,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildStatCard(
                        'Ověřené zastávky',
                        '${_crowdsourcingStats!.verifiedStops}',
                        Icons.verified,
                        Colors.orange,
                      ),
                      const SizedBox(width: 12),
                      _buildStatCard(
                        'Přesnost',
                        '${(_crowdsourcingStats!.averageAccuracy * 100).toStringAsFixed(1)}%',
                        Icons.check_circle,
                        Colors.purple,
                      ),
                    ],
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(fontSize: 12, color: color),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContributeCard() {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.volunteer_activism, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Přispějte komunitě',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),

            const Text(
              'Pomozte vylepšit dopravní data pro všechny uživatele:',
              style: TextStyle(fontSize: 14),
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showReportDialog(UserReportType.delay),
                    icon: const Icon(Icons.schedule),
                    label: const Text('Nahlásit zpoždění'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () =>
                        _showReportDialog(UserReportType.occupancy),
                    icon: const Icon(Icons.people),
                    label: const Text('Obsazenost'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showReportDialog(UserReportType.newStop),
                icon: const Icon(Icons.add_location),
                label: const Text('Přidat novou zastávku'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showReportDialog(UserReportType type) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getReportTitle(type)),
        content: Text(_getReportDescription(type)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _submitReport(type);
            },
            child: const Text('Odeslat'),
          ),
        ],
      ),
    );
  }

  String _getReportTitle(UserReportType type) {
    switch (type) {
      case UserReportType.delay:
        return 'Nahlásit zpoždění';
      case UserReportType.occupancy:
        return 'Nahlásit obsazenost';
      case UserReportType.newStop:
        return 'Přidat zastávku';
    }
  }

  String _getReportDescription(UserReportType type) {
    switch (type) {
      case UserReportType.delay:
        return 'Nahlaste zpoždění vozidla, které právě čekáte.';
      case UserReportType.occupancy:
        return 'Sdělte, jak je vozidlo obsazené.';
      case UserReportType.newStop:
        return 'Přidejte novou zastávku, která chybí v mapě.';
    }
  }

  Future<void> _submitReport(UserReportType type) async {
    // Zde by byla implementace formuláře pro konkrétní typ reportu
    // Pro demo účely pouze zobrazíme snackbar

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${_getReportTitle(type)} byl odeslán. Děkujeme!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'právě teď';
    } else if (difference.inMinutes < 60) {
      return 'před ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'před ${difference.inHours} h';
    } else {
      return 'před ${difference.inDays} dny';
    }
  }
}

enum UserReportType { delay, occupancy, newStop }
