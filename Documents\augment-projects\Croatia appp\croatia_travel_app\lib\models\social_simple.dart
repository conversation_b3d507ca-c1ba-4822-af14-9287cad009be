import 'transport.dart';

// ========== SDÍLENÉ TRASY ==========

class SharedRoute {
  final String id;
  final String routeId;
  final TransportRoute route;
  final String title;
  final String? description;
  final String authorId;
  final String authorName;
  final String? authorAvatar;
  final List<String> tags;
  final SharePrivacy privacy;
  final bool allowComments;
  final bool allowRatings;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int viewCount;
  final int likeCount;
  final int shareCount;
  final double averageRating;
  final int reviewCount;
  final bool isBookmarked;
  final bool isLiked;
  final List<String>? photos;

  SharedRoute({
    required this.id,
    required this.routeId,
    required this.route,
    required this.title,
    this.description,
    required this.authorId,
    required this.authorName,
    this.authorAvatar,
    required this.tags,
    required this.privacy,
    required this.allowComments,
    required this.allowRatings,
    required this.createdAt,
    required this.updatedAt,
    required this.viewCount,
    required this.likeCount,
    required this.shareCount,
    required this.averageRating,
    required this.reviewCount,
    required this.isBookmarked,
    required this.isLiked,
    this.photos,
  });

  SharedRoute copyWith({
    String? id,
    String? routeId,
    TransportRoute? route,
    String? title,
    String? description,
    String? authorId,
    String? authorName,
    String? authorAvatar,
    List<String>? tags,
    SharePrivacy? privacy,
    bool? allowComments,
    bool? allowRatings,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? viewCount,
    int? likeCount,
    int? shareCount,
    double? averageRating,
    int? reviewCount,
    bool? isBookmarked,
    bool? isLiked,
    List<String>? photos,
  }) {
    return SharedRoute(
      id: id ?? this.id,
      routeId: routeId ?? this.routeId,
      route: route ?? this.route,
      title: title ?? this.title,
      description: description ?? this.description,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      tags: tags ?? this.tags,
      privacy: privacy ?? this.privacy,
      allowComments: allowComments ?? this.allowComments,
      allowRatings: allowRatings ?? this.allowRatings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      viewCount: viewCount ?? this.viewCount,
      likeCount: likeCount ?? this.likeCount,
      shareCount: shareCount ?? this.shareCount,
      averageRating: averageRating ?? this.averageRating,
      reviewCount: reviewCount ?? this.reviewCount,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      isLiked: isLiked ?? this.isLiked,
      photos: photos ?? this.photos,
    );
  }
}

// ========== HODNOCENÍ TRAS ==========

class RouteReview {
  final String id;
  final String routeId;
  final String userId;
  final String userName;
  final String? userAvatar;
  final double rating; // 1.0 - 5.0
  final String? comment;
  final List<String> pros;
  final List<String> cons;
  final Map<String, double> categoryRatings; // např. "comfort": 4.5
  final DateTime createdAt;
  final DateTime updatedAt;
  final int helpfulCount;
  final bool isHelpful;
  final bool isVerified;
  final List<String>? photos;

  RouteReview({
    required this.id,
    required this.routeId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.rating,
    this.comment,
    required this.pros,
    required this.cons,
    required this.categoryRatings,
    required this.createdAt,
    required this.updatedAt,
    required this.helpfulCount,
    required this.isHelpful,
    required this.isVerified,
    this.photos,
  });
}

// ========== UŽIVATELSKÉ STATISTIKY ==========

class UserSocialStats {
  final String userId;
  final int sharedRoutesCount;
  final int reviewsCount;
  final int followersCount;
  final int followingCount;
  final int totalLikesReceived;
  final int totalViewsReceived;
  final double averageRating;
  final int helpfulReviewsCount;
  final List<String> topTags;
  final DateTime memberSince;
  final UserLevel level;
  final int experiencePoints;

  UserSocialStats({
    required this.userId,
    required this.sharedRoutesCount,
    required this.reviewsCount,
    required this.followersCount,
    required this.followingCount,
    required this.totalLikesReceived,
    required this.totalViewsReceived,
    required this.averageRating,
    required this.helpfulReviewsCount,
    required this.topTags,
    required this.memberSince,
    required this.level,
    required this.experiencePoints,
  });
}

// ========== ENUMS ==========

enum SharePrivacy { public, friends, private }

enum ShareSortBy { newest, oldest, mostLiked, mostViewed, bestRated }

enum ReviewSortBy { newest, oldest, highestRated, lowestRated, mostHelpful }

enum ContentType { route, review, comment }

enum ReportReason { 
  spam, 
  inappropriate, 
  misleading, 
  copyright, 
  harassment, 
  other 
}

enum UserLevel { 
  beginner, 
  explorer, 
  traveler, 
  expert, 
  master 
}

// ========== UTILITY CLASSES ==========

class SocialUtils {
  /// Formátování času pro sociální sítě
  static String formatSocialTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'právě teď';
    } else if (difference.inMinutes < 60) {
      return 'před ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'před ${difference.inHours} h';
    } else if (difference.inDays < 7) {
      return 'před ${difference.inDays} dny';
    } else {
      return '${dateTime.day}.${dateTime.month}.${dateTime.year}';
    }
  }

  /// Formátování počtu lajků/zobrazení
  static String formatCount(int count) {
    if (count < 1000) return count.toString();
    if (count < 1000000) return '${(count / 1000).toStringAsFixed(1)}k';
    return '${(count / 1000000).toStringAsFixed(1)}M';
  }

  /// Získání názvu úrovně uživatele
  static String getLevelName(UserLevel level) {
    switch (level) {
      case UserLevel.beginner:
        return 'Začátečník';
      case UserLevel.explorer:
        return 'Průzkumník';
      case UserLevel.traveler:
        return 'Cestovatel';
      case UserLevel.expert:
        return 'Expert';
      case UserLevel.master:
        return 'Mistr';
    }
  }

  /// Validace hodnocení
  static bool isValidRating(double rating) {
    return rating >= 1.0 && rating <= 5.0;
  }

  /// Výpočet úrovně na základě XP
  static UserLevel calculateLevel(int experiencePoints) {
    if (experiencePoints < 100) return UserLevel.beginner;
    if (experiencePoints < 500) return UserLevel.explorer;
    if (experiencePoints < 1500) return UserLevel.traveler;
    if (experiencePoints < 5000) return UserLevel.expert;
    return UserLevel.master;
  }
}
