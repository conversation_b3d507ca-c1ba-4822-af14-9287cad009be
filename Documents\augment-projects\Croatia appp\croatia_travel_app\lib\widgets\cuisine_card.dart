import 'package:flutter/material.dart';
import '../models/cuisine.dart';

class CuisineCard extends StatelessWidget {
  final CuisineItem cuisineItem;
  final VoidCallback? onTap;
  final bool showRecipeIcon;

  const CuisineCard({
    super.key,
    required this.cuisineItem,
    this.onTap,
    this.showRecipeIcon = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Obrázek nebo placeholder
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                gradient: LinearGradient(
                  colors: _getGradientColors(cuisineItem.type),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      _getCuisineIcon(cuisineItem.type),
                      size: 40,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),

                  // Dietní ozna<PERSON>í
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Row(
                      children: [
                        if (cuisineItem.isVegan)
                          _buildDietBadge('V', Colors.green),
                        if (cuisineItem.isVegetarian && !cuisineItem.isVegan)
                          _buildDietBadge('VEG', Colors.lightGreen),
                        if (cuisineItem.isGlutenFree)
                          _buildDietBadge('GF', Colors.orange),
                      ],
                    ),
                  ),

                  // Recept ikona
                  if (showRecipeIcon && cuisineItem.recipe != null)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.menu_book,
                          size: 16,
                          color: Colors.orange,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Informace o pokrmu
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          cuisineItem.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (cuisineItem.rating != null)
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 14,
                              color: Colors.amber[600],
                            ),
                            const SizedBox(width: 2),
                            Text(
                              cuisineItem.rating!.toStringAsFixed(1),
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  Text(
                    cuisineItem.description,
                    style: TextStyle(fontSize: 13, color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Ingredience (první 3)
                  if (cuisineItem.ingredients.isNotEmpty)
                    Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: cuisineItem.ingredients
                          .take(3)
                          .map(
                            (ingredient) => Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                ingredient,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),

                  const SizedBox(height: 8),

                  // Spodní řádek s informacemi
                  Row(
                    children: [
                      // Typ pokrmu
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getTypeColor(
                            cuisineItem.type,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getTypeLabel(cuisineItem.type),
                          style: TextStyle(
                            fontSize: 10,
                            color: _getTypeColor(cuisineItem.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                      const SizedBox(width: 6),

                      // Region
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _getRegionLabel(cuisineItem.region),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                      const Spacer(),

                      // Cena
                      if (cuisineItem.averagePrice != null)
                        Text(
                          '${cuisineItem.averagePrice!.toStringAsFixed(0)}€',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDietBadge(String text, Color color) {
    return Container(
      margin: const EdgeInsets.only(right: 4),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  List<Color> _getGradientColors(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return [Colors.red.shade400, Colors.red.shade600];
      case CuisineType.appetizer:
        return [Colors.green.shade400, Colors.green.shade600];
      case CuisineType.dessert:
        return [Colors.pink.shade400, Colors.pink.shade600];
      case CuisineType.drink:
        return [Colors.blue.shade400, Colors.blue.shade600];
      case CuisineType.snack:
        return [Colors.orange.shade400, Colors.orange.shade600];
      case CuisineType.soup:
        return [Colors.amber.shade400, Colors.amber.shade600];
      case CuisineType.seafood:
        return [Colors.cyan.shade400, Colors.cyan.shade600];
      case CuisineType.meat:
        return [Colors.brown.shade400, Colors.brown.shade600];
      case CuisineType.pasta:
        return [Colors.yellow.shade400, Colors.yellow.shade600];
      case CuisineType.bread:
        return [Colors.deepOrange.shade400, Colors.deepOrange.shade600];
    }
  }

  IconData _getCuisineIcon(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return Icons.dinner_dining;
      case CuisineType.appetizer:
        return Icons.tapas;
      case CuisineType.dessert:
        return Icons.cake;
      case CuisineType.drink:
        return Icons.local_drink;
      case CuisineType.snack:
        return Icons.fastfood;
      case CuisineType.soup:
        return Icons.soup_kitchen;
      case CuisineType.seafood:
        return Icons.set_meal;
      case CuisineType.meat:
        return Icons.kebab_dining;
      case CuisineType.pasta:
        return Icons.ramen_dining;
      case CuisineType.bread:
        return Icons.bakery_dining;
    }
  }

  Color _getTypeColor(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return Colors.red;
      case CuisineType.appetizer:
        return Colors.green;
      case CuisineType.dessert:
        return Colors.pink;
      case CuisineType.drink:
        return Colors.blue;
      case CuisineType.snack:
        return Colors.orange;
      case CuisineType.soup:
        return Colors.amber;
      case CuisineType.seafood:
        return Colors.cyan;
      case CuisineType.meat:
        return Colors.brown;
      case CuisineType.pasta:
        return Colors.yellow;
      case CuisineType.bread:
        return Colors.deepOrange;
    }
  }

  String _getTypeLabel(CuisineType type) {
    switch (type) {
      case CuisineType.mainDish:
        return 'Hlavní chod';
      case CuisineType.appetizer:
        return 'Předkrm';
      case CuisineType.dessert:
        return 'Dezert';
      case CuisineType.drink:
        return 'Nápoj';
      case CuisineType.snack:
        return 'Svačina';
      case CuisineType.soup:
        return 'Polévka';
      case CuisineType.seafood:
        return 'Mořské plody';
      case CuisineType.meat:
        return 'Maso';
      case CuisineType.pasta:
        return 'Těstoviny';
      case CuisineType.bread:
        return 'Pečivo';
    }
  }

  String _getRegionLabel(String region) {
    switch (region) {
      case 'istria':
        return 'Istrie';
      case 'dalmatia':
        return 'Dalmácie';
      case 'slavonia':
        return 'Slavonie';
      case 'lika':
        return 'Lika';
      case 'zagreb':
        return 'Zagreb';
      case 'all':
        return 'Celé Chorvatsko';
      default:
        return region;
    }
  }
}
