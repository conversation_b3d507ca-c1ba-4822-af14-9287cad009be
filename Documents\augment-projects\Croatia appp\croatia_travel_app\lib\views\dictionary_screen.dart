import 'package:flutter/material.dart';

class DictionaryScreen extends StatefulWidget {
  const DictionaryScreen({super.key});

  @override
  State<DictionaryScreen> createState() => _DictionaryScreenState();
}

class _DictionaryScreenState extends State<DictionaryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  List<DictionaryEntry> _allPhrases = [];
  List<DictionaryEntry> _filteredPhrases = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPhrases();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadPhrases() {
    _allPhrases = [
      // Základní fráze
      DictionaryEntry(
        czech: 'Dobrý den',
        croatian: 'Dobar dan',
        pronunciation: 'dobar dan',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: '<PERSON><PERSON><PERSON><PERSON>',
        croatian: '<PERSON>vala',
        pronunciation: 'hvala',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Prosím',
        croatian: '<PERSON><PERSON>',
        pronunciation: 'molim',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Promiňte',
        croatian: 'Oprostite',
        pronunciation: 'oprostite',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Ano',
        croatian: 'Da',
        pronunciation: 'da',
        category: 'basic',
      ),
      DictionaryEntry(
        czech: 'Ne',
        croatian: 'Ne',
        pronunciation: 'ne',
        category: 'basic',
      ),

      // Cestování
      DictionaryEntry(
        czech: 'Kde je nádraží?',
        croatian: 'Gdje je kolodvor?',
        pronunciation: 'gdje je kolodvor',
        category: 'travel',
      ),
      DictionaryEntry(
        czech: 'Kolik to stojí?',
        croatian: 'Koliko to košta?',
        pronunciation: 'koliko to košta',
        category: 'travel',
      ),
      DictionaryEntry(
        czech: 'Jízdenka',
        croatian: 'Karta',
        pronunciation: 'karta',
        category: 'travel',
      ),
      DictionaryEntry(
        czech: 'Hotel',
        croatian: 'Hotel',
        pronunciation: 'hotel',
        category: 'travel',
      ),

      // Jídlo
      DictionaryEntry(
        czech: 'Restaurace',
        croatian: 'Restoran',
        pronunciation: 'restoran',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Jídelní lístek',
        croatian: 'Jelovnik',
        pronunciation: 'jelovnik',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Pivo',
        croatian: 'Pivo',
        pronunciation: 'pivo',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Víno',
        croatian: 'Vino',
        pronunciation: 'vino',
        category: 'food',
      ),
      DictionaryEntry(
        czech: 'Ryba',
        croatian: 'Riba',
        pronunciation: 'riba',
        category: 'food',
      ),

      // Nouzové situace
      DictionaryEntry(
        czech: 'Pomoc!',
        croatian: 'Upomoć!',
        pronunciation: 'upomoć',
        category: 'emergency',
      ),
      DictionaryEntry(
        czech: 'Zavolejte doktora',
        croatian: 'Pozovite doktora',
        pronunciation: 'pozovite doktora',
        category: 'emergency',
      ),
      DictionaryEntry(
        czech: 'Nemocnice',
        croatian: 'Bolnica',
        pronunciation: 'bolnica',
        category: 'emergency',
      ),
    ];

    _filteredPhrases = List.from(_allPhrases);
    setState(() {});
  }

  void _filterPhrases() {
    setState(() {
      if (_searchQuery.isEmpty) {
        _filteredPhrases = List.from(_allPhrases);
      } else {
        _filteredPhrases = _allPhrases.where((phrase) {
          return phrase.czech.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              phrase.croatian.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              phrase.pronunciation.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              );
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chorvatský slovník'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.star), text: 'Základní'),
            Tab(icon: Icon(Icons.flight), text: 'Cestování'),
            Tab(icon: Icon(Icons.restaurant), text: 'Jídlo'),
            Tab(icon: Icon(Icons.emergency), text: 'Nouzové'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Vyhledávací pole
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'Hledat fráze...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _filterPhrases();
              },
            ),
          ),

          // Obsah podle tabů
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPhrasesList('basic'),
                _buildPhrasesList('travel'),
                _buildPhrasesList('food'),
                _buildPhrasesList('emergency'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhrasesList(String category) {
    final categoryPhrases = _filteredPhrases
        .where((phrase) => phrase.category == category)
        .toList();

    if (categoryPhrases.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné fráze nenalezeny',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categoryPhrases.length,
      itemBuilder: (context, index) {
        final phrase = categoryPhrases[index];
        return _buildPhraseCard(phrase);
      },
    );
  }

  Widget _buildPhraseCard(DictionaryEntry phrase) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Česky
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'CZ',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    phrase.czech,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Chorvatsky
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'HR',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    phrase.croatian,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _playPronunciation(phrase),
                  icon: const Icon(Icons.volume_up),
                  tooltip: 'Přehrát výslovnost',
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Výslovnost
            Row(
              children: [
                Icon(
                  Icons.record_voice_over,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Text(
                  '[${phrase.pronunciation}]',
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _playPronunciation(DictionaryEntry phrase) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Přehrávání: ${phrase.croatian}'),
        duration: const Duration(seconds: 1),
      ),
    );
    // Zde by byla implementace text-to-speech
  }
}

// Model pro slovníkové záznamy
class DictionaryEntry {
  final String czech;
  final String croatian;
  final String pronunciation;
  final String category;

  DictionaryEntry({
    required this.czech,
    required this.croatian,
    required this.pronunciation,
    required this.category,
  });
}
