import 'package:flutter/material.dart';
import '../models/event.dart';
import '../services/notification_service.dart';
import '../widgets/event_card.dart';
import '../widgets/calendar_widget.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({super.key});

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> with TickerProviderStateMixin {
  List<Event> _allEvents = [];
  List<Event> _filteredEvents = [];
  String _selectedRegion = 'all';
  EventType? _selectedType;
  bool _showBookmarkedOnly = false;
  bool _showUpcomingOnly = true;
  
  late TabController _tabController;
  final NotificationService _notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadEvents();
    _initializeNotifications();
  }

  Future<void> _loadEvents() async {
    // Simulace načtení událostí z databáze
    _allEvents = [
      Event(
        id: '1',
        name: 'Dubrovník Summer Festival',
        description: 'Prestižní kulturní festival s divadelními představeními, koncerty a tanečními vystoupeními na historických místech Dubrovníku.',
        startDate: DateTime.now().add(const Duration(days: 30)),
        endDate: DateTime.now().add(const Duration(days: 45)),
        location: 'Dubrovník - různá místa',
        latitude: 42.6407,
        longitude: 18.1077,
        region: 'dalmatia',
        type: EventType.cultural,
        images: ['dubrovnik_festival1.jpg'],
        website: 'https://www.dubrovnik-festival.hr',
        isFree: false,
        price: 50.0,
        tags: ['kultura', 'divadlo', 'hudba'],
        isBookmarked: true,
      ),
      Event(
        id: '2',
        name: 'Ultra Europe',
        description: 'Jeden z největších elektronických hudebních festivalů v Evropě.',
        startDate: DateTime.now().add(const Duration(days: 60)),
        endDate: DateTime.now().add(const Duration(days: 62)),
        location: 'Split - Poljud Stadium',
        latitude: 43.5081,
        longitude: 16.4402,
        region: 'dalmatia',
        type: EventType.concert,
        images: ['ultra_europe1.jpg'],
        website: 'https://ultraeurope.com',
        isFree: false,
        price: 150.0,
        tags: ['elektronická hudba', 'festival', 'tanec'],
        isBookmarked: false,
      ),
      Event(
        id: '3',
        name: 'Truffle Days',
        description: 'Festival věnovaný istrským lanýžům s degustacemi a kulinářskými workshopy.',
        startDate: DateTime.now().add(const Duration(days: 15)),
        endDate: DateTime.now().add(const Duration(days: 17)),
        location: 'Motovun, Istrie',
        latitude: 45.3364,
        longitude: 13.8286,
        region: 'istria',
        type: EventType.food,
        images: ['truffle_days1.jpg'],
        isFree: false,
        price: 25.0,
        tags: ['gastronomie', 'lanýže', 'Istrie'],
        isBookmarked: true,
      ),
      Event(
        id: '4',
        name: 'Špancirfest',
        description: 'Největší street art festival v Chorvatsku ve Varaždinu.',
        startDate: DateTime.now().add(const Duration(days: 45)),
        endDate: DateTime.now().add(const Duration(days: 55)),
        location: 'Varaždin',
        latitude: 46.3044,
        longitude: 16.3378,
        region: 'zagreb',
        type: EventType.cultural,
        isFree: true,
        tags: ['street art', 'kultura', 'zdarma'],
        isBookmarked: false,
      ),
      Event(
        id: '5',
        name: 'Regata Barcolana',
        description: 'Jedna z největších plachetních regat na světě.',
        startDate: DateTime.now().add(const Duration(days: 90)),
        endDate: DateTime.now().add(const Duration(days: 92)),
        location: 'Terst (blízko chorvatských hranic)',
        latitude: 45.6495,
        longitude: 13.7768,
        region: 'istria',
        type: EventType.sports,
        isFree: true,
        tags: ['plachetnice', 'sport', 'moře'],
        isBookmarked: false,
      ),
    ];

    _filteredEvents = List.from(_allEvents);
    _filterEvents();
    setState(() {});
  }

  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
    
    // Nastavení notifikací pro bookmarkované události
    for (final event in _allEvents.where((e) => e.isBookmarked)) {
      await _scheduleEventNotification(event);
    }
  }

  Future<void> _scheduleEventNotification(Event event) async {
    // Notifikace 1 den před událostí
    final notificationTime = event.startDate.subtract(const Duration(days: 1));
    
    if (notificationTime.isAfter(DateTime.now())) {
      await _notificationService.scheduleNotification(
        id: event.id.hashCode,
        title: 'Připomínka události',
        body: '${event.name} začíná zítra!',
        scheduledDate: notificationTime,
      );
    }
  }

  void _filterEvents() {
    List<Event> filtered = List.from(_allEvents);

    // Filtrování podle regionu
    if (_selectedRegion != 'all') {
      filtered = filtered.where((event) => event.region == _selectedRegion).toList();
    }

    // Filtrování podle typu
    if (_selectedType != null) {
      filtered = filtered.where((event) => event.type == _selectedType).toList();
    }

    // Filtrování bookmarkovaných
    if (_showBookmarkedOnly) {
      filtered = filtered.where((event) => event.isBookmarked).toList();
    }

    // Filtrování nadcházejících událostí
    if (_showUpcomingOnly) {
      filtered = filtered.where((event) => event.endDate.isAfter(DateTime.now())).toList();
    }

    // Řazení podle data
    filtered.sort((a, b) => a.startDate.compareTo(b.startDate));

    setState(() {
      _filteredEvents = filtered;
    });
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filtry událostí',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Region
            DropdownButtonFormField<String>(
              value: _selectedRegion,
              decoration: const InputDecoration(labelText: 'Region'),
              items: const [
                DropdownMenuItem(value: 'all', child: Text('Všechny regiony')),
                DropdownMenuItem(value: 'istria', child: Text('Istrie')),
                DropdownMenuItem(value: 'dalmatia', child: Text('Dalmácie')),
                DropdownMenuItem(value: 'slavonia', child: Text('Slavonie')),
                DropdownMenuItem(value: 'zagreb', child: Text('Zagreb a okolí')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedRegion = value!;
                });
                _filterEvents();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Typ události
            DropdownButtonFormField<EventType?>(
              value: _selectedType,
              decoration: const InputDecoration(labelText: 'Typ události'),
              items: const [
                DropdownMenuItem(value: null, child: Text('Všechny typy')),
                DropdownMenuItem(value: EventType.cultural, child: Text('Kulturní')),
                DropdownMenuItem(value: EventType.concert, child: Text('Koncerty')),
                DropdownMenuItem(value: EventType.food, child: Text('Gastronomie')),
                DropdownMenuItem(value: EventType.sports, child: Text('Sport')),
                DropdownMenuItem(value: EventType.festival, child: Text('Festivaly')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedType = value;
                });
                _filterEvents();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Přepínače
            SwitchListTile(
              title: const Text('Pouze bookmarkované'),
              value: _showBookmarkedOnly,
              onChanged: (value) {
                setState(() {
                  _showBookmarkedOnly = value;
                });
                _filterEvents();
              },
            ),
            
            SwitchListTile(
              title: const Text('Pouze nadcházející'),
              value: _showUpcomingOnly,
              onChanged: (value) {
                setState(() {
                  _showUpcomingOnly = value;
                });
                _filterEvents();
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Události a festivaly'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Seznam', icon: Icon(Icons.list)),
            Tab(text: 'Kalendář', icon: Icon(Icons.calendar_month)),
            Tab(text: 'Mapa', icon: Icon(Icons.map)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Seznam událostí
          _buildEventsList(),
          
          // Kalendářové zobrazení
          CalendarWidget(
            events: _filteredEvents,
            onEventTap: _navigateToEventDetail,
          ),
          
          // Mapové zobrazení
          _buildEventsMap(),
        ],
      ),
    );
  }

  Widget _buildEventsList() {
    if (_filteredEvents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Žádné události nenalezeny',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Zkuste změnit filtry',
              style: TextStyle(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredEvents.length,
      itemBuilder: (context, index) {
        final event = _filteredEvents[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: EventCard(
            event: event,
            onTap: () => _navigateToEventDetail(event),
            onBookmarkToggle: () => _toggleBookmark(event),
          ),
        );
      },
    );
  }

  Widget _buildEventsMap() {
    return const Center(
      child: Text(
        'Mapové zobrazení událostí\n(Implementováno v MapScreen)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  void _navigateToEventDetail(Event event) {
    Navigator.pushNamed(context, '/events/${event.id}');
  }

  Future<void> _toggleBookmark(Event event) async {
    setState(() {
      final index = _allEvents.indexWhere((e) => e.id == event.id);
      if (index != -1) {
        _allEvents[index] = event.copyWith(
          isBookmarked: !event.isBookmarked,
        );
      }
    });

    // Nastavení/zrušení notifikace
    if (!event.isBookmarked) {
      await _scheduleEventNotification(event);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Událost přidána do bookmarků a nastavena připomínka'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      await _notificationService.cancelNotification(event.id.hashCode);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Událost odebrána z bookmarků'),
          ),
        );
      }
    }

    _filterEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
