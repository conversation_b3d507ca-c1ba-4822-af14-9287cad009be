import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/transport.dart';
import '../services/transport_service.dart';

class IdosStyleSearchWidget extends StatefulWidget {
  const IdosStyleSearchWidget({super.key});

  @override
  State<IdosStyleSearchWidget> createState() => _IdosStyleSearchWidgetState();
}

class _IdosStyleSearchWidgetState extends State<IdosStyleSearchWidget> {
  final TransportService _transportService = TransportService();
  final TextEditingController _fromController = TextEditingController();
  final TextEditingController _toController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isDeparture = true; // true = odjezd, false = příjezd
  bool _isSearching = false;

  List<TransportRoute> _searchResults = [];

  @override
  void initState() {
    super.initState();
    _loadCurrentLocation();
  }

  @override
  void dispose() {
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition();
      final address = await _getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );
      setState(() {
        _fromController.text = address;
      });
    } catch (e) {
      // Ignorovat chybu, uživatel může zadat ručně
    }
  }

  Future<String> _getAddressFromCoordinates(double lat, double lng) async {
    // Simulace reverse geocoding
    return "Aktuální poloha";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Column(
        children: [
          _buildSearchForm(),
          Expanded(
            child: _isSearching
                ? _buildLoadingState()
                : _searchResults.isEmpty
                ? _buildEmptyState()
                : _buildSearchResults(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchForm() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2)),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Hlavička s logem
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1976D2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.directions_transit,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Vyhledání spojení',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF1976D2),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Formulář vyhledávání
              _buildLocationInputs(),
              const SizedBox(height: 16),
              _buildDateTimeSelector(),
              const SizedBox(height: 16),
              _buildSearchButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationInputs() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Odkud
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: Color(0xFF4CAF50),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _fromController,
                    decoration: const InputDecoration(
                      hintText: 'Odkud',
                      border: InputBorder.none,
                      hintStyle: TextStyle(color: Colors.grey),
                    ),
                    onChanged: _onFromTextChanged,
                  ),
                ),
                IconButton(
                  onPressed: _getCurrentLocation,
                  icon: const Icon(Icons.my_location, color: Colors.grey),
                ),
              ],
            ),
          ),

          // Divider s tlačítkem pro prohození
          Container(
            height: 1,
            color: Colors.grey.shade300,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _swapLocations,
                    icon: const Icon(Icons.swap_vert, color: Color(0xFF1976D2)),
                    iconSize: 20,
                  ),
                ),
              ],
            ),
          ),

          // Kam
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    color: Color(0xFFF44336),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _toController,
                    decoration: const InputDecoration(
                      hintText: 'Kam',
                      border: InputBorder.none,
                      hintStyle: TextStyle(color: Colors.grey),
                    ),
                    onChanged: _onToTextChanged,
                  ),
                ),
                IconButton(
                  onPressed: _showFavorites,
                  icon: const Icon(Icons.star_border, color: Colors.grey),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeSelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Datum
          InkWell(
            onTap: _selectDate,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today, color: Color(0xFF1976D2)),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _formatDate(_selectedDate),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const Icon(Icons.arrow_drop_down, color: Colors.grey),
                ],
              ),
            ),
          ),

          Container(height: 1, color: Colors.grey.shade300),

          // Čas a typ
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Icon(Icons.access_time, color: Color(0xFF1976D2)),
                const SizedBox(width: 12),
                InkWell(
                  onTap: _selectTime,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _formatTime(_selectedTime),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('Odjezd'),
                          value: true,
                          groupValue: _isDeparture,
                          onChanged: (value) =>
                              setState(() => _isDeparture = value!),
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<bool>(
                          title: const Text('Příjezd'),
                          value: false,
                          groupValue: _isDeparture,
                          onChanged: (value) =>
                              setState(() => _isDeparture = value!),
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isSearching ? null : _searchConnections,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1976D2),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isSearching
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Vyhledat spojení',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Hledám nejlepší spojení...'),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.directions_transit, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'Zadejte výchozí a cílové místo',
            style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'Najdeme pro vás nejlepší spojení',
            style: TextStyle(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final route = _searchResults[index];
        return _buildRouteCard(route, index);
      },
    );
  }

  Widget _buildRouteCard(TransportRoute route, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showRouteDetails(route),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hlavička s časem a cenou
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_formatTime(TimeOfDay.fromDateTime(DateTime.now()))} → ${_formatTime(TimeOfDay.fromDateTime(DateTime.now().add(route.totalDuration)))}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Doba jízdy: ${route.totalDuration.inMinutes} min',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${route.totalPrice.toStringAsFixed(0)} ${route.currency}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1976D2),
                        ),
                      ),
                      if (route.transfers > 0)
                        Text(
                          '${route.transfers} přestup${route.transfers > 1 ? 'y' : ''}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Vizualizace trasy
              _buildRouteVisualization(route),

              const SizedBox(height: 8),

              // Dodatečné informace
              Row(
                children: [
                  if (_getWalkingDistance(route) > 0) ...[
                    Icon(
                      Icons.directions_walk,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${_getWalkingDistance(route)}m chůze',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  Icon(Icons.eco, size: 16, color: Colors.green.shade600),
                  const SizedBox(width: 4),
                  Text(
                    'Ekologické',
                    style: TextStyle(
                      color: Colors.green.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRouteVisualization(TransportRoute route) {
    return SizedBox(
      height: 40,
      child: Row(
        children: route.segments.asMap().entries.map((entry) {
          final index = entry.key;
          final segment = entry.value;

          return Expanded(
            flex: segment.duration.inMinutes,
            child: Container(
              margin: EdgeInsets.only(
                right: index < route.segments.length - 1 ? 2 : 0,
              ),
              decoration: BoxDecoration(
                color: _getSegmentColor(segment.type),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Center(
                child: Text(
                  segment.routeNumber ?? _getSegmentLabel(segment.type),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Color _getSegmentColor(SegmentType type) {
    switch (type) {
      case SegmentType.bus:
        return const Color(0xFF2196F3);
      case SegmentType.tram:
        return const Color(0xFF4CAF50);
      case SegmentType.metro:
        return const Color(0xFFF44336);
      case SegmentType.train:
        return const Color(0xFF9C27B0);
      case SegmentType.ferry:
        return const Color(0xFF00BCD4);
      case SegmentType.walking:
        return const Color(0xFF795548);
      default:
        return const Color(0xFF757575);
    }
  }

  String _getSegmentLabel(SegmentType type) {
    switch (type) {
      case SegmentType.walking:
        return '🚶';
      case SegmentType.waiting:
        return '⏱️';
      default:
        return '';
    }
  }

  // Event handlers
  void _onFromTextChanged(String value) {
    // Implementace našeptávače pro výchozí místo
  }

  void _onToTextChanged(String value) {
    // Implementace našeptávače pro cílové místo
  }

  void _getCurrentLocation() async {
    await _loadCurrentLocation();
  }

  void _swapLocations() {
    final temp = _fromController.text;
    _fromController.text = _toController.text;
    _toController.text = temp;
  }

  void _showFavorites() {
    // Implementace oblíbených míst
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  Future<void> _searchConnections() async {
    if (_fromController.text.isEmpty || _toController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Vyplňte výchozí i cílové místo')),
      );
      return;
    }

    setState(() => _isSearching = true);

    try {
      // Simulace vyhledávání - v reálné aplikaci by se volalo API
      await Future.delayed(const Duration(seconds: 2));

      final results = await _transportService.searchRoutes(
        from: _fromController.text,
        to: _toController.text,
        departureTime: DateTime(
          _selectedDate.year,
          _selectedDate.month,
          _selectedDate.day,
          _selectedTime.hour,
          _selectedTime.minute,
        ),
        isDeparture: _isDeparture,
      );

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() => _isSearching = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Chyba při vyhledávání: $e')));
      }
    }
  }

  void _showRouteDetails(TransportRoute route) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => RouteDetailScreen(route: route)),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    if (date.day == now.day &&
        date.month == now.month &&
        date.year == now.year) {
      return 'Dnes';
    } else if (date.day == now.day + 1 &&
        date.month == now.month &&
        date.year == now.year) {
      return 'Zítra';
    } else {
      return '${date.day}.${date.month}.${date.year}';
    }
  }

  String _formatTime(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // Pomocné metody
  int _getWalkingDistance(TransportRoute route) {
    return route.segments
        .where((segment) => segment.type == SegmentType.walking)
        .fold(0, (sum, segment) => sum + segment.distance.round());
  }
}

// Placeholder pro detail trasy
class RouteDetailScreen extends StatelessWidget {
  final TransportRoute route;

  const RouteDetailScreen({super.key, required this.route});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Detail trasy')),
      body: const Center(child: Text('Detail trasy - implementace')),
    );
  }
}
