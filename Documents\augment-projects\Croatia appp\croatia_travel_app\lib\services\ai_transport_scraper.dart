import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import '../models/transport_simple.dart';

/// AI-powered web scraper pro chorvatské dopravní systémy
class AITransportScraper {
  static final AITransportScraper _instance = AITransportScraper._internal();
  factory AITransportScraper() => _instance;
  AITransportScraper._internal();

  final Dio _dio = Dio();
  final Map<String, DateTime> _lastScrapeTime = {};
  final Map<String, dynamic> _cachedData = {};

  // Konfigurace pro různé weby
  static const Map<String, ScrapingConfig> _configs = {
    'zagreb_zet': ScrapingConfig(
      baseUrl: 'https://www.zet.hr',
      timetableUrl: 'https://www.zet.hr/vozni-red',
      stopsUrl: 'https://www.zet.hr/stajalista',
      realTimeUrl: 'https://www.zet.hr/info-promet',
      selectors: {
        'stops': '.stajaliste-item',
        'routes': '.linija-item',
        'times': '.vrijeme-item',
        'delays': '.kasnjenje-info',
      },
    ),
    'split_promet': ScrapingConfig(
      baseUrl: 'https://www.promet-split.hr',
      timetableUrl: 'https://www.promet-split.hr/vozni-red',
      stopsUrl: 'https://www.promet-split.hr/stajalista',
      realTimeUrl: 'https://www.promet-split.hr/uzivo',
      selectors: {
        'stops': '.stop-info',
        'routes': '.route-info',
        'times': '.time-info',
        'delays': '.delay-info',
      },
    ),
    'rijeka_autotrolej': ScrapingConfig(
      baseUrl: 'https://www.autotrolej.hr',
      timetableUrl: 'https://www.autotrolej.hr/red-voznje',
      stopsUrl: 'https://www.autotrolej.hr/stajalista',
      realTimeUrl: 'https://www.autotrolej.hr/info',
      selectors: {
        'stops': '.stajaliste',
        'routes': '.linija',
        'times': '.vrijeme',
        'delays': '.kasnjenje',
      },
    ),
  };

  /// Inicializace scraperu
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept':
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'hr-HR,hr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    );

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: false,
          responseBody: false,
          logPrint: (obj) => debugPrint('[AI Scraper] $obj'),
        ),
      );
    }
  }

  /// Získání zastávek pomocí AI parsingu
  Future<List<TransportStop>> getStopsForCity(String cityId) async {
    final cacheKey = 'stops_$cityId';

    // Kontrola cache (1 hodina)
    if (_isCacheValid(cacheKey, const Duration(hours: 1))) {
      return _cachedData[cacheKey] ?? [];
    }

    try {
      final config = _configs['${cityId}_${_getCityProvider(cityId)}'];
      if (config == null) {
        return _getMockStops(cityId);
      }

      final stops = await _scrapeStops(config);
      _cachedData[cacheKey] = stops;
      _lastScrapeTime[cacheKey] = DateTime.now();

      return stops;
    } catch (e) {
      debugPrint('AI Scraping chyba pro $cityId: $e');
      return _getMockStops(cityId);
    }
  }

  /// Získání real-time dat pomocí AI
  Future<List<RealTimeArrival>> getRealTimeArrivals(
    String stopId,
    String cityId,
  ) async {
    final cacheKey = 'realtime_${stopId}_$cityId';

    // Kontrola cache (30 sekund)
    if (_isCacheValid(cacheKey, const Duration(seconds: 30))) {
      return _cachedData[cacheKey] ?? [];
    }

    try {
      final config = _configs['${cityId}_${_getCityProvider(cityId)}'];
      if (config == null) {
        return _getMockRealTimeArrivals(stopId);
      }

      final arrivals = await _scrapeRealTimeData(config, stopId);
      _cachedData[cacheKey] = arrivals;
      _lastScrapeTime[cacheKey] = DateTime.now();

      return arrivals;
    } catch (e) {
      debugPrint('AI Real-time scraping chyba: $e');
      return _getMockRealTimeArrivals(stopId);
    }
  }

  /// Získání tras pomocí AI
  Future<List<PublicTransport>> getRoutesForCity(String cityId) async {
    final cacheKey = 'routes_$cityId';

    if (_isCacheValid(cacheKey, const Duration(hours: 6))) {
      return _cachedData[cacheKey] ?? [];
    }

    try {
      final config = _configs['${cityId}_${_getCityProvider(cityId)}'];
      if (config == null) {
        return _getMockRoutes(cityId);
      }

      final routes = await _scrapeRoutes(config, cityId);
      _cachedData[cacheKey] = routes;
      _lastScrapeTime[cacheKey] = DateTime.now();

      return routes;
    } catch (e) {
      debugPrint('AI Routes scraping chyba: $e');
      return _getMockRoutes(cityId);
    }
  }

  /// AI-powered scraping zastávek
  Future<List<TransportStop>> _scrapeStops(ScrapingConfig config) async {
    final response = await _dio.get(config.stopsUrl);
    final document = html_parser.parse(response.data);

    final stops = <TransportStop>[];

    // AI parsing - hledáme různé možné struktury
    final stopElements = _findStopElements(document, config);

    for (final element in stopElements) {
      final stop = _parseStopElement(element);
      if (stop != null) {
        stops.add(stop);
      }
    }

    return stops;
  }

  /// AI-powered scraping real-time dat
  Future<List<RealTimeArrival>> _scrapeRealTimeData(
    ScrapingConfig config,
    String stopId,
  ) async {
    try {
      // Pokusíme se najít real-time data pro konkrétní zastávku
      final url = '${config.realTimeUrl}?stop=$stopId';
      final response = await _dio.get(url);
      final document = html_parser.parse(response.data);

      final arrivals = <RealTimeArrival>[];

      // AI parsing real-time dat
      final arrivalElements = _findArrivalElements(document, config);

      for (final element in arrivalElements) {
        final arrival = _parseArrivalElement(element);
        if (arrival != null) {
          arrivals.add(arrival);
        }
      }

      return arrivals;
    } catch (e) {
      // Fallback na obecná real-time data
      return _scrapeGeneralRealTimeData(config);
    }
  }

  /// AI-powered scraping tras
  Future<List<PublicTransport>> _scrapeRoutes(
    ScrapingConfig config,
    String cityId,
  ) async {
    final response = await _dio.get(config.timetableUrl);
    final document = html_parser.parse(response.data);

    final routes = <PublicTransport>[];

    // AI parsing tras
    final routeElements = _findRouteElements(document, config);

    for (final element in routeElements) {
      final route = _parseRouteElement(element, cityId);
      if (route != null) {
        routes.add(route);
      }
    }

    return routes;
  }

  /// AI algoritmus pro hledání elementů zastávek
  List<Element> _findStopElements(Document document, ScrapingConfig config) {
    final elements = <Element>[];

    // 1. Zkusíme konfigurovaný selektor
    elements.addAll(document.querySelectorAll(config.selectors['stops'] ?? ''));

    // 2. AI heuristiky - hledáme typické vzory
    if (elements.isEmpty) {
      // Hledáme podle class názvů
      elements.addAll(
        document.querySelectorAll('.stop, .stajaliste, .station, .parada'),
      );
      elements.addAll(
        document.querySelectorAll('[class*="stop"], [class*="stajaliste"]'),
      );

      // Hledáme podle data atributů
      elements.addAll(document.querySelectorAll('[data-stop], [data-station]'));

      // Hledáme podle ID vzorů
      elements.addAll(
        document.querySelectorAll('[id*="stop"], [id*="station"]'),
      );
    }

    // 3. Pokud stále nic, hledáme podle obsahu
    if (elements.isEmpty) {
      final allDivs = document.querySelectorAll('div, li, tr');
      for (final div in allDivs) {
        final text = div.text.toLowerCase();
        if (text.contains('stajalište') ||
            text.contains('zastávka') ||
            text.contains('station') ||
            text.contains('stop')) {
          elements.add(div);
        }
      }
    }

    return elements;
  }

  /// AI parsing jednotlivé zastávky
  TransportStop? _parseStopElement(Element element) {
    try {
      // AI extrakce dat z elementu
      final name = _extractStopName(element);
      final coordinates = _extractCoordinates(element);
      final id = _extractStopId(element);

      if (name.isEmpty || id.isEmpty) return null;

      return TransportStop(
        id: id,
        name: name,
        latitude: coordinates['lat'] ?? 45.8150,
        longitude: coordinates['lng'] ?? 15.9819,
        city: 'zagreb', // Default
        zone: _extractZone(element),
        platforms: _extractPlatforms(element),
        facilities: _extractFacilities(element),
        isAccessible: _extractAccessibility(element),
        hasRealTimeInfo: _extractRealTimeAvailability(element),
      );
    } catch (e) {
      debugPrint('Chyba při parsování zastávky: $e');
      return null;
    }
  }

  /// AI extrakce názvu zastávky
  String _extractStopName(Element element) {
    // Hledáme v různých možných místech
    final selectors = [
      '.name',
      '.naziv',
      '.stop-name',
      '.stajaliste-naziv',
      'h1',
      'h2',
      'h3',
      '.title',
      '.naslov',
      '[data-name]',
      '[data-stop-name]',
    ];

    for (final selector in selectors) {
      final nameElement = element.querySelector(selector);
      if (nameElement != null && nameElement.text.trim().isNotEmpty) {
        return nameElement.text.trim();
      }
    }

    // Fallback - hledáme v textu elementu
    final text = element.text.trim();
    if (text.isNotEmpty && text.length < 100) {
      return text;
    }

    return 'Neznámá zastávka';
  }

  /// AI extrakce souřadnic
  Map<String, double> _extractCoordinates(Element element) {
    // Hledáme data atributy
    final lat =
        element.attributes['data-lat'] ??
        element.attributes['data-latitude'] ??
        element.attributes['lat'];
    final lng =
        element.attributes['data-lng'] ??
        element.attributes['data-longitude'] ??
        element.attributes['lng'];

    if (lat != null && lng != null) {
      return {
        'lat': double.tryParse(lat) ?? 45.8150,
        'lng': double.tryParse(lng) ?? 15.9819,
      };
    }

    // Hledáme v onclick nebo href atributech
    final onclick = element.attributes['onclick'] ?? '';
    final href = element.attributes['href'] ?? '';

    final coordRegex = RegExp(r'(\d+\.\d+),\s*(\d+\.\d+)');
    final match = coordRegex.firstMatch('$onclick $href');

    if (match != null) {
      return {
        'lat': double.tryParse(match.group(1)!) ?? 45.8150,
        'lng': double.tryParse(match.group(2)!) ?? 15.9819,
      };
    }

    // Default Zagreb coordinates
    return {'lat': 45.8150, 'lng': 15.9819};
  }

  /// AI extrakce ID zastávky
  String _extractStopId(Element element) {
    // Hledáme ID v různých atributech
    final id =
        element.attributes['id'] ??
        element.attributes['data-id'] ??
        element.attributes['data-stop-id'] ??
        element.attributes['value'];

    if (id != null && id.isNotEmpty) {
      return id;
    }

    // Generujeme ID z názvu
    final name = _extractStopName(element);
    return 'stop_${name.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), '_')}';
  }

  // Pomocné AI extrakční metody
  String? _extractZone(Element element) {
    final zoneElement = element.querySelector('.zone, .zona, [data-zone]');
    return zoneElement?.text.trim();
  }

  List<String> _extractPlatforms(Element element) {
    final platformElements = element.querySelectorAll(
      '.platform, .peron, [data-platform]',
    );
    return platformElements
        .map((e) => e.text.trim())
        .where((t) => t.isNotEmpty)
        .toList();
  }

  List<String> _extractFacilities(Element element) {
    final facilities = <String>[];

    // Hledáme ikony nebo text indikující vybavení
    if (element.querySelector('.wheelchair, .pristupacnost') != null) {
      facilities.add('wheelchair');
    }
    if (element.querySelector('.shelter, .nadstresek') != null) {
      facilities.add('shelter');
    }
    if (element.querySelector('.display, .displej') != null) {
      facilities.add('display');
    }

    return facilities;
  }

  bool _extractAccessibility(Element element) {
    return element.querySelector('.wheelchair, .pristupacnost, .accessible') !=
        null;
  }

  bool _extractRealTimeAvailability(Element element) {
    return element.querySelector('.realtime, .uzivo, .live') != null;
  }

  // Další AI parsing metody...
  List<Element> _findArrivalElements(Document document, ScrapingConfig config) {
    // Implementace podobná _findStopElements
    return document.querySelectorAll(
      config.selectors['times'] ?? '.arrival, .dolazak, .vrijeme',
    );
  }

  List<Element> _findRouteElements(Document document, ScrapingConfig config) {
    // Implementace podobná _findStopElements
    return document.querySelectorAll(
      config.selectors['routes'] ?? '.route, .linija, .ruta',
    );
  }

  RealTimeArrival? _parseArrivalElement(Element element) {
    // AI parsing příjezdů
    try {
      final routeNumber = _extractRouteNumber(element);
      final direction = _extractDirection(element);
      final time = _extractArrivalTime(element);

      if (routeNumber.isEmpty || direction.isEmpty) return null;

      return RealTimeArrival(
        routeNumber: routeNumber,
        direction: direction,
        scheduledTime: time,
        estimatedTime: time,
        isRealTime: _extractIsRealTime(element),
      );
    } catch (e) {
      return null;
    }
  }

  PublicTransport? _parseRouteElement(Element element, String cityId) {
    // AI parsing tras
    try {
      final routeNumber = _extractRouteNumber(element);
      final routeName = _extractRouteName(element);

      if (routeNumber.isEmpty) return null;

      return PublicTransport(
        id: 'route_${cityId}_$routeNumber',
        city: cityId,
        type: _guessTransportType(routeNumber, routeName),
        routeNumber: routeNumber,
        routeName: routeName,
        direction: _extractDirection(element),
        stops: [],
        operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        startTime: '05:00',
        endTime: '23:00',
        frequency: 15,
        price: 4.0,
        currency: 'HRK',
        isActive: true,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      return null;
    }
  }

  // Pomocné metody pro extrakci dat
  String _extractRouteNumber(Element element) {
    final selectors = [
      '.route-number',
      '.broj-linije',
      '.line-number',
      '.broj',
    ];
    for (final selector in selectors) {
      final el = element.querySelector(selector);
      if (el != null) return el.text.trim();
    }

    // Hledáme čísla v textu
    final numberRegex = RegExp(r'\b(\d+[A-Z]?)\b');
    final match = numberRegex.firstMatch(element.text);
    return match?.group(1) ?? '';
  }

  String _extractDirection(Element element) {
    final selectors = ['.direction', '.smjer', '.smer', '.cilj'];
    for (final selector in selectors) {
      final el = element.querySelector(selector);
      if (el != null) return el.text.trim();
    }
    return 'Neznámý směr';
  }

  String _extractRouteName(Element element) {
    final selectors = ['.route-name', '.naziv-linije', '.line-name'];
    for (final selector in selectors) {
      final el = element.querySelector(selector);
      if (el != null) return el.text.trim();
    }
    return 'Neznámá linka';
  }

  DateTime _extractArrivalTime(Element element) {
    final selectors = ['.time', '.vrijeme', '.dolazak', '.arrival'];
    for (final selector in selectors) {
      final el = element.querySelector(selector);
      if (el != null) {
        final timeStr = el.text.trim();
        final time = _parseTimeString(timeStr);
        if (time != null) return time;
      }
    }
    return DateTime.now().add(const Duration(minutes: 5));
  }

  bool _extractIsRealTime(Element element) {
    return element.querySelector('.realtime, .live, .uzivo') != null ||
        element.text.toLowerCase().contains('uživo');
  }

  DateTime? _parseTimeString(String timeStr) {
    // Parsování různých formátů času
    final patterns = [
      RegExp(r'(\d{1,2}):(\d{2})'),
      RegExp(r'(\d{1,2})\.(\d{2})'),
      RegExp(r'(\d+)\s*min'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(timeStr);
      if (match != null) {
        if (timeStr.contains('min')) {
          final minutes = int.tryParse(match.group(1)!) ?? 0;
          return DateTime.now().add(Duration(minutes: minutes));
        } else {
          final hour = int.tryParse(match.group(1)!) ?? 0;
          final minute = int.tryParse(match.group(2)!) ?? 0;
          final now = DateTime.now();
          return DateTime(now.year, now.month, now.day, hour, minute);
        }
      }
    }

    return null;
  }

  TransportType _guessTransportType(String routeNumber, String routeName) {
    final text = '$routeNumber $routeName'.toLowerCase();

    if (text.contains('tramvaj') || text.contains('tram')) {
      return TransportType.tram;
    }
    if (text.contains('vlak') || text.contains('train')) {
      return TransportType.train;
    }
    if (text.contains('trajekt') || text.contains('ferry')) {
      return TransportType.ferry;
    }

    return TransportType.bus; // Default
  }

  Future<List<RealTimeArrival>> _scrapeGeneralRealTimeData(
    ScrapingConfig config,
  ) async {
    // Fallback implementace
    return _getMockRealTimeArrivals('general');
  }

  String _getCityProvider(String cityId) {
    switch (cityId.toLowerCase()) {
      case 'zagreb':
        return 'zet';
      case 'split':
        return 'promet';
      case 'rijeka':
        return 'autotrolej';
      default:
        return 'unknown';
    }
  }

  bool _isCacheValid(String key, Duration maxAge) {
    final lastTime = _lastScrapeTime[key];
    if (lastTime == null) return false;
    return DateTime.now().difference(lastTime) < maxAge;
  }

  // Mock data metody (stejné jako v předchozích implementacích)
  List<TransportStop> _getMockStops(String cityId) {
    return [
      TransportStop(
        id: '${cityId}_stop_001',
        name: 'Hlavní nádraží',
        latitude: 45.8050,
        longitude: 15.9819,
        city: cityId,
        zone: '1',
        platforms: ['A', 'B'],
        facilities: ['shelter', 'bench'],
        isAccessible: true,
        hasRealTimeInfo: true,
      ),
    ];
  }

  List<RealTimeArrival> _getMockRealTimeArrivals(String stopId) {
    final now = DateTime.now();
    return [
      RealTimeArrival(
        routeNumber: '6',
        direction: 'Črnomerec',
        scheduledTime: now.add(const Duration(minutes: 3)),
        estimatedTime: now.add(const Duration(minutes: 4)),
        delay: const Duration(minutes: 1),
        isRealTime: true,
      ),
    ];
  }

  List<PublicTransport> _getMockRoutes(String cityId) {
    return [
      PublicTransport(
        id: 'route_${cityId}_6',
        city: cityId,
        type: TransportType.tram,
        routeNumber: '6',
        routeName: 'Črnomerec - Sopot',
        direction: 'Sopot',
        stops: [],
        operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        startTime: '05:00',
        endTime: '23:00',
        frequency: 10,
        price: 4.0,
        currency: 'HRK',
        isActive: true,
        lastUpdated: DateTime.now(),
      ),
    ];
  }

  void clearCache() {
    _cachedData.clear();
    _lastScrapeTime.clear();
  }
}

/// Konfigurace pro scraping jednotlivých webů
class ScrapingConfig {
  final String baseUrl;
  final String timetableUrl;
  final String stopsUrl;
  final String realTimeUrl;
  final Map<String, String> selectors;

  const ScrapingConfig({
    required this.baseUrl,
    required this.timetableUrl,
    required this.stopsUrl,
    required this.realTimeUrl,
    required this.selectors,
  });
}
