import 'dart:typed_data';
import 'dart:math' as math;

// ========== OFFLINE MAP MODELY ==========

class OfflineMapData {
  final String id;
  final String regionId;
  final String regionName;
  final MapBounds bounds;
  final DateTime downloadedAt;
  final DateTime lastUsed;
  final int sizeBytes;
  final bool isComplete;
  final String version;
  final List<MapTile> tiles;

  OfflineMapData({
    required this.id,
    required this.regionId,
    required this.regionName,
    required this.bounds,
    required this.downloadedAt,
    required this.lastUsed,
    required this.sizeBytes,
    required this.isComplete,
    required this.version,
    required this.tiles,
  });

  OfflineMapData copyWith({
    String? id,
    String? regionId,
    String? regionName,
    MapBounds? bounds,
    DateTime? downloadedAt,
    DateTime? lastUsed,
    int? sizeBytes,
    bool? isComplete,
    String? version,
    List<MapTile>? tiles,
  }) {
    return OfflineMapData(
      id: id ?? this.id,
      regionId: regionId ?? this.regionId,
      regionName: regionName ?? this.regionName,
      bounds: bounds ?? this.bounds,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      lastUsed: lastUsed ?? this.lastUsed,
      sizeBytes: sizeBytes ?? this.sizeBytes,
      isComplete: isComplete ?? this.isComplete,
      version: version ?? this.version,
      tiles: tiles ?? this.tiles,
    );
  }
}

class MapBounds {
  final double north;
  final double south;
  final double east;
  final double west;

  MapBounds({
    required this.north,
    required this.south,
    required this.east,
    required this.west,
  });

  bool contains(double lat, double lng) {
    return lat >= south && lat <= north && lng >= west && lng <= east;
  }

  double get centerLat => (north + south) / 2;
  double get centerLng => (east + west) / 2;
}

class MapTile {
  final int x;
  final int y;
  final int z;
  final Uint8List data;
  final DateTime downloadedAt;

  MapTile({
    required this.x,
    required this.y,
    required this.z,
    required this.data,
    required this.downloadedAt,
  });
}

class MapRegion {
  final String id;
  final String name;
  final String nameEn;
  final MapBounds bounds;
  final double estimatedSize; // MB
  final MapPriority priority;
  final MapType type;

  MapRegion({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.bounds,
    required this.estimatedSize,
    required this.priority,
    required this.type,
  });
}

class TileCoordinate {
  final int x;
  final int y;
  final int z;

  TileCoordinate({
    required this.x,
    required this.y,
    required this.z,
  });

  @override
  String toString() => 'TileCoordinate(x: $x, y: $y, z: $z)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TileCoordinate &&
        other.x == x &&
        other.y == y &&
        other.z == z;
  }

  @override
  int get hashCode => x.hashCode ^ y.hashCode ^ z.hashCode;
}

class MapDownloadProgress {
  final String regionId;
  final String regionName;
  final double progress; // 0.0 - 1.0
  final int downloadedTiles;
  final int totalTiles;
  final DownloadStatus status;
  final Duration estimatedTimeRemaining;

  MapDownloadProgress({
    required this.regionId,
    required this.regionName,
    required this.progress,
    required this.downloadedTiles,
    required this.totalTiles,
    required this.status,
    required this.estimatedTimeRemaining,
  });
}

// ========== ENUMS ==========

enum MapPriority { high, medium, low }

enum MapType { city, region, country }

enum DownloadStatus { 
  waiting, 
  downloading, 
  completed, 
  error, 
  paused, 
  cancelled 
}

// ========== UTILITY CLASSES ==========

class MapUtils {
  /// Výpočet vzdálenosti mezi dvěma body
  static double calculateDistance(
    double lat1, double lng1, 
    double lat2, double lng2
  ) {
    const double earthRadius = 6371000; // metry
    final double dLat = _toRadians(lat2 - lat1);
    final double dLng = _toRadians(lng2 - lng1);
    
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_toRadians(lat1)) * math.cos(_toRadians(lat2)) *
        math.sin(dLng / 2) * math.sin(dLng / 2);
    
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  /// Kontrola, zda je bod v bounds
  static bool isPointInBounds(double lat, double lng, MapBounds bounds) {
    return bounds.contains(lat, lng);
  }

  /// Převod na radiány
  static double _toRadians(double degrees) {
    return degrees * (math.pi / 180.0);
  }

  /// Formátování velikosti souboru
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
