import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/offline_map_simple.dart';
import 'dart:math' as math;

class OfflineMapService {
  static final OfflineMapService _instance = OfflineMapService._internal();
  factory OfflineMapService() => _instance;
  OfflineMapService._internal();

  final Dio _dio = Dio();

  // Callbacks pro progress
  final StreamController<MapDownloadProgress> _downloadProgressController =
      StreamController<MapDownloadProgress>.broadcast();
  Stream<MapDownloadProgress> get downloadProgress =>
      _downloadProgressController.stream;

  // Cache pro offline tiles
  final Map<String, Uint8List> _tileCache = {};
  final List<OfflineMapData> _downloadedMaps = [];

  // Dostupné regiony pro stažení
  final List<MapRegion> _availableRegions = [
    MapRegion(
      id: 'zagreb',
      name: 'Zagreb',
      nameEn: 'Zagreb',
      bounds: MapBounds(
        north: 45.8500,
        south: 45.7500,
        east: 16.0500,
        west: 15.9000,
      ),
      estimatedSize: 45.2, // MB
      priority: MapPriority.high,
      type: MapType.city,
    ),
    MapRegion(
      id: 'split',
      name: 'Split',
      nameEn: 'Split',
      bounds: MapBounds(
        north: 43.5500,
        south: 43.4800,
        east: 16.5000,
        west: 16.4000,
      ),
      estimatedSize: 32.1,
      priority: MapPriority.high,
      type: MapType.city,
    ),
    MapRegion(
      id: 'dubrovnik',
      name: 'Dubrovnik',
      nameEn: 'Dubrovnik',
      bounds: MapBounds(
        north: 42.6800,
        south: 42.6200,
        east: 18.1500,
        west: 18.0500,
      ),
      estimatedSize: 28.7,
      priority: MapPriority.medium,
      type: MapType.city,
    ),
  ];

  /// Získání dostupných regionů
  List<MapRegion> getAvailableRegions() => _availableRegions;

  /// Získání stažených regionů
  Future<List<OfflineMapData>> getDownloadedMaps() async {
    return _downloadedMaps;
  }

  /// Kontrola, zda je region stažený
  Future<bool> isRegionDownloaded(String regionId) async {
    return _downloadedMaps.any(
      (map) => map.regionId == regionId && map.isComplete,
    );
  }

  /// Stažení mapy regionu
  Future<bool> downloadRegion(
    String regionId, {
    bool forceUpdate = false,
  }) async {
    try {
      final region = _availableRegions.firstWhere((r) => r.id == regionId);

      // Kontrola, zda už není stažená
      if (!forceUpdate && await isRegionDownloaded(regionId)) {
        debugPrint('Region $regionId už je stažený');
        return true;
      }

      // Vytvoření záznamu o stahování
      final mapData = OfflineMapData(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        regionId: regionId,
        regionName: region.name,
        bounds: region.bounds,
        downloadedAt: DateTime.now(),
        lastUsed: DateTime.now(),
        sizeBytes: 0,
        isComplete: false,
        version: '1.0',
        tiles: [],
      );

      _downloadedMaps.add(mapData);

      // Spuštění stahování na pozadí
      _downloadRegionTiles(region, mapData);

      return true;
    } catch (e) {
      debugPrint('Chyba při stahování regionu $regionId: $e');
      return false;
    }
  }

  /// Stahování tiles pro region
  Future<void> _downloadRegionTiles(
    MapRegion region,
    OfflineMapData mapData,
  ) async {
    try {
      final tiles = _generateTileList(region.bounds);
      final totalTiles = tiles.length;
      int downloadedTiles = 0;

      _downloadProgressController.add(
        MapDownloadProgress(
          regionId: region.id,
          regionName: region.name,
          progress: 0.0,
          downloadedTiles: 0,
          totalTiles: totalTiles,
          status: DownloadStatus.downloading,
          estimatedTimeRemaining: Duration.zero,
        ),
      );

      final List<MapTile> downloadedTilesList = [];
      final startTime = DateTime.now();

      for (final tileCoord in tiles) {
        try {
          final tileData = await _downloadTile(tileCoord);
          if (tileData != null) {
            final tile = MapTile(
              x: tileCoord.x,
              y: tileCoord.y,
              z: tileCoord.z,
              data: tileData,
              downloadedAt: DateTime.now(),
            );
            downloadedTilesList.add(tile);
            _tileCache[_getTileKey(tileCoord)] = tileData;
          }

          downloadedTiles++;
          final progress = downloadedTiles / totalTiles;

          // Odhad zbývajícího času
          final elapsed = DateTime.now().difference(startTime);
          final estimatedTotal = elapsed * (totalTiles / downloadedTiles);
          final remaining = estimatedTotal - elapsed;

          _downloadProgressController.add(
            MapDownloadProgress(
              regionId: region.id,
              regionName: region.name,
              progress: progress,
              downloadedTiles: downloadedTiles,
              totalTiles: totalTiles,
              status: DownloadStatus.downloading,
              estimatedTimeRemaining: remaining,
            ),
          );

          // Pauza mezi stahováním pro úsporu baterie
          await Future.delayed(const Duration(milliseconds: 50));
        } catch (e) {
          debugPrint('Chyba při stahování tile $tileCoord: $e');
        }
      }

      // Aktualizace mapy jako dokončené
      final index = _downloadedMaps.indexWhere((m) => m.id == mapData.id);
      if (index != -1) {
        _downloadedMaps[index] = mapData.copyWith(
          tiles: downloadedTilesList,
          sizeBytes: downloadedTilesList.fold<int>(
            0,
            (sum, tile) => sum + tile.data.length,
          ),
          isComplete: true,
        );
      }

      _downloadProgressController.add(
        MapDownloadProgress(
          regionId: region.id,
          regionName: region.name,
          progress: 1.0,
          downloadedTiles: downloadedTiles,
          totalTiles: totalTiles,
          status: DownloadStatus.completed,
          estimatedTimeRemaining: Duration.zero,
        ),
      );

      debugPrint('Stahování regionu ${region.name} dokončeno');
    } catch (e) {
      debugPrint('Chyba při stahování tiles: $e');
      _downloadProgressController.add(
        MapDownloadProgress(
          regionId: region.id,
          regionName: region.name,
          progress: 0.0,
          downloadedTiles: 0,
          totalTiles: 0,
          status: DownloadStatus.error,
          estimatedTimeRemaining: Duration.zero,
        ),
      );
    }
  }

  /// Generování seznamu tiles pro region
  List<TileCoordinate> _generateTileList(MapBounds bounds) {
    final List<TileCoordinate> tiles = [];

    // Různé zoom levely pro detailní mapy
    for (int zoom = 10; zoom <= 16; zoom++) {
      final minX = _lonToTileX(bounds.west, zoom);
      final maxX = _lonToTileX(bounds.east, zoom);
      final minY = _latToTileY(bounds.north, zoom);
      final maxY = _latToTileY(bounds.south, zoom);

      for (int x = minX; x <= maxX; x++) {
        for (int y = minY; y <= maxY; y++) {
          tiles.add(TileCoordinate(x: x, y: y, z: zoom));
        }
      }
    }

    return tiles;
  }

  /// Stažení jednotlivé tile
  Future<Uint8List?> _downloadTile(TileCoordinate coord) async {
    try {
      // OpenStreetMap tile server
      final url =
          'https://tile.openstreetmap.org/${coord.z}/${coord.x}/${coord.y}.png';

      final response = await _dio.get(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        return Uint8List.fromList(response.data);
      }
    } catch (e) {
      debugPrint('Chyba při stahování tile: $e');
    }
    return null;
  }

  /// Získání tile z cache
  Future<Uint8List?> getTile(TileCoordinate coord) async {
    final key = _getTileKey(coord);

    // Zkusit cache
    if (_tileCache.containsKey(key)) {
      return _tileCache[key];
    }

    return null;
  }

  /// Smazání offline mapy
  Future<bool> deleteOfflineMap(String regionId) async {
    try {
      _downloadedMaps.removeWhere((map) => map.regionId == regionId);

      // Vyčištění cache
      _tileCache.removeWhere((key, value) => key.startsWith(regionId));

      return true;
    } catch (e) {
      debugPrint('Chyba při mazání offline mapy: $e');
      return false;
    }
  }

  /// Aktualizace času posledního použití
  Future<void> updateLastUsed(String regionId) async {
    // Mock implementation
  }

  /// Vyčištění starých map
  Future<void> cleanupOldMaps({int maxAgeInDays = 30}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: maxAgeInDays));
    _downloadedMaps.removeWhere((map) => map.lastUsed.isBefore(cutoffDate));
  }

  /// Získání celkové velikosti offline map
  Future<int> getTotalOfflineSize() async {
    return _downloadedMaps.fold<int>(0, (sum, map) => sum + map.sizeBytes);
  }

  // Utility metody pro tile koordináty
  int _lonToTileX(double lon, int zoom) {
    return ((lon + 180.0) / 360.0 * (1 << zoom)).floor();
  }

  int _latToTileY(double lat, int zoom) {
    final latRad = lat * (math.pi / 180.0);
    return ((1.0 -
                (math.log(math.tan(latRad) + (1.0 / math.cos(latRad))) /
                    math.pi)) /
            2.0 *
            (1 << zoom))
        .floor();
  }

  String _getTileKey(TileCoordinate coord) {
    return '${coord.z}_${coord.x}_${coord.y}';
  }

  /// Dispose
  void dispose() {
    _downloadProgressController.close();
  }
}
