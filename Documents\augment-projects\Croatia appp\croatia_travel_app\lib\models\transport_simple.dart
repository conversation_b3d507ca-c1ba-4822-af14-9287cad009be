// Zjednodušené modely pro skutečné dopravní API

// ========== VEŘEJNÁ DOPRAVA ==========

class PublicTransport {
  final String id;
  final String city;
  final TransportType type;
  final String routeNumber;
  final String routeName;
  final String direction;
  final List<TransportStop> stops;
  final List<String> operatingDays;
  final String startTime;
  final String endTime;
  final int frequency; // v minutách
  final double price;
  final String currency;
  final bool isActive;
  final DateTime lastUpdated;

  PublicTransport({
    required this.id,
    required this.city,
    required this.type,
    required this.routeNumber,
    required this.routeName,
    required this.direction,
    required this.stops,
    required this.operatingDays,
    required this.startTime,
    required this.endTime,
    required this.frequency,
    required this.price,
    required this.currency,
    required this.isActive,
    required this.lastUpdated,
  });
}

class TransportStop {
  final String id;
  final String name;
  final double latitude;
  final double longitude;
  final String city;
  final String? zone;
  final List<String> platforms;
  final List<String> facilities;
  final bool isAccessible;
  final bool hasRealTimeInfo;
  final List<RealTimeArrival> arrivals;

  TransportStop({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
    required this.city,
    this.zone,
    required this.platforms,
    required this.facilities,
    required this.isAccessible,
    this.hasRealTimeInfo = false,
    this.arrivals = const [],
  });
}

class RealTimeArrival {
  final String routeNumber;
  final String direction;
  final DateTime scheduledTime;
  final DateTime? estimatedTime;
  final Duration? delay;
  final String? vehicleId;
  final bool isRealTime;
  final String? message;

  RealTimeArrival({
    required this.routeNumber,
    required this.direction,
    required this.scheduledTime,
    this.estimatedTime,
    this.delay,
    this.vehicleId,
    this.isRealTime = false,
    this.message,
  });

  Duration get timeToArrival {
    final now = DateTime.now();
    final arrivalTime = estimatedTime ?? scheduledTime;
    return arrivalTime.difference(now);
  }

  bool get isDelayed => delay != null && delay!.inMinutes > 0;
}

// ========== JÍZDENKY ==========

class Ticket {
  final String id;
  final TicketType type;
  final String city;
  final double price;
  final String currency;
  final DateTime purchaseTime;
  final DateTime validFrom;
  final DateTime validUntil;
  final TicketStatus status;
  final String qrCode;
  final List<String>? validRoutes;
  final String? userId;

  Ticket({
    required this.id,
    required this.type,
    required this.city,
    required this.price,
    required this.currency,
    required this.purchaseTime,
    required this.validFrom,
    required this.validUntil,
    required this.status,
    required this.qrCode,
    this.validRoutes,
    this.userId,
  });

  bool get isValid {
    final now = DateTime.now();
    return status == TicketStatus.active &&
        now.isAfter(validFrom) &&
        now.isBefore(validUntil);
  }

  Duration get remainingTime => validUntil.difference(DateTime.now());
}

// ========== PLÁNOVÁNÍ TRAS ==========

class TransportRoute {
  final String id;
  final String fromStopId;
  final String toStopId;
  final List<RouteSegment> segments;
  final Duration totalDuration;
  final int totalDistance; // v metrech
  final double totalPrice;
  final String currency;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final int transfers;
  final RouteType routeType;
  final List<String> warnings;

  TransportRoute({
    required this.id,
    required this.fromStopId,
    required this.toStopId,
    required this.segments,
    required this.totalDuration,
    required this.totalDistance,
    required this.totalPrice,
    required this.currency,
    required this.departureTime,
    required this.arrivalTime,
    required this.transfers,
    required this.routeType,
    this.warnings = const [],
  });
}

class RouteSegment {
  final SegmentType type;
  final String? routeNumber;
  final String? fromStopName;
  final String? toStopName;
  final DateTime? departureTime;
  final DateTime? arrivalTime;
  final Duration duration;
  final int distance; // v metrech
  final double? price;
  final List<LatLng> path;
  final String? instructions;

  RouteSegment({
    required this.type,
    this.routeNumber,
    this.fromStopName,
    this.toStopName,
    this.departureTime,
    this.arrivalTime,
    required this.duration,
    required this.distance,
    this.price,
    this.path = const [],
    this.instructions,
  });
}

class LatLng {
  final double latitude;
  final double longitude;

  LatLng({required this.latitude, required this.longitude});
}

// ========== NOVÉ MODELY PRO SKUTEČNÉ API ==========

class TrainConnection {
  final String id;
  final String trainNumber;
  final String trainType;
  final String fromStation;
  final String toStation;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final Duration duration;
  final double price;
  final String currency;
  final List<String> carriageClasses;
  final bool hasReservation;
  final bool isActive;

  TrainConnection({
    required this.id,
    required this.trainNumber,
    required this.trainType,
    required this.fromStation,
    required this.toStation,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.price,
    required this.currency,
    required this.carriageClasses,
    required this.hasReservation,
    required this.isActive,
  });
}

class FerryConnection {
  final String id;
  final String lineNumber;
  final String shipName;
  final String fromPort;
  final String toPort;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final Duration duration;
  final double price;
  final String currency;
  final double? vehiclePrice;
  final bool hasVehicleDeck;
  final bool isActive;

  FerryConnection({
    required this.id,
    required this.lineNumber,
    required this.shipName,
    required this.fromPort,
    required this.toPort,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.price,
    required this.currency,
    this.vehiclePrice,
    required this.hasVehicleDeck,
    required this.isActive,
  });
}

class RealTimeInfo {
  final String stopId;
  final String routeNumber;
  final String direction;
  final DateTime estimatedArrival;
  final Duration? delay;
  final String? vehicleId;
  final bool isRealTime;

  RealTimeInfo({
    required this.stopId,
    required this.routeNumber,
    required this.direction,
    required this.estimatedArrival,
    this.delay,
    this.vehicleId,
    required this.isRealTime,
  });
}

class TrafficAlert {
  final String id;
  final String title;
  final String description;
  final AlertSeverity severity;
  final DateTime startTime;
  final DateTime? endTime;
  final List<String> affectedRoutes;
  final String? location;
  final bool isActive;

  TrafficAlert({
    required this.id,
    required this.title,
    required this.description,
    required this.severity,
    required this.startTime,
    this.endTime,
    required this.affectedRoutes,
    this.location,
    required this.isActive,
  });
}

// ========== ENUMS ==========

enum TransportType { bus, tram, metro, train, ferry, trolleybus }

enum TicketType { single, daily, weekly, monthly, annual, multiRide, tourist }

enum TicketStatus { active, expired, used, cancelled, pending }

enum RouteType { fastest, cheapest, leastTransfers, walking, mixed }

enum SegmentType { walking, bus, tram, metro, train, ferry, waiting }

enum TransportMode { walking, publicTransport, bike, car, taxi, scooter }

enum AlertSeverity { low, medium, high, critical }

enum OccupancyLevel { empty, low, medium, high, full, unknown }

// ========== UTILITY CLASSES ==========

class TransportUtils {
  /// Formátování času pro dopravní spojení
  static String formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// Formátování doby trvání
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}min';
    } else {
      return '${minutes}min';
    }
  }

  /// Formátování vzdálenosti
  static String formatDistance(int meters) {
    if (meters < 1000) {
      return '${meters}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(1)}km';
    }
  }

  /// Získání názvu typu dopravy
  static String getTransportTypeName(TransportType type) {
    switch (type) {
      case TransportType.bus:
        return 'Autobus';
      case TransportType.tram:
        return 'Tramvaj';
      case TransportType.metro:
        return 'Metro';
      case TransportType.train:
        return 'Vlak';
      case TransportType.ferry:
        return 'Trajekt';
      case TransportType.trolleybus:
        return 'Trolejbus';
    }
  }

  /// Získání ikony pro typ dopravy
  static String getTransportTypeIcon(TransportType type) {
    switch (type) {
      case TransportType.bus:
        return '🚌';
      case TransportType.tram:
        return '🚋';
      case TransportType.metro:
        return '🚇';
      case TransportType.train:
        return '🚆';
      case TransportType.ferry:
        return '⛴️';
      case TransportType.trolleybus:
        return '🚎';
    }
  }

  /// Kontrola platnosti jízdenky
  static bool isTicketValid(Ticket ticket) {
    return ticket.isValid;
  }

  /// Výpočet celkové ceny trasy
  static double calculateTotalPrice(List<RouteSegment> segments) {
    return segments.fold(0.0, (sum, segment) => sum + (segment.price ?? 0.0));
  }
}
