import 'package:flutter/material.dart';
import '../models/offline_map_simple.dart';
import '../services/offline_map_service_simple.dart';

class OfflineMapsWidget extends StatefulWidget {
  const OfflineMapsWidget({super.key});

  @override
  State<OfflineMapsWidget> createState() => _OfflineMapsWidgetState();
}

class _OfflineMapsWidgetState extends State<OfflineMapsWidget> {
  final OfflineMapService _mapService = OfflineMapService();

  List<MapRegion> _availableRegions = [];
  List<OfflineMapData> _downloadedMaps = [];
  final Map<String, MapDownloadProgress> _downloadProgress = {};
  bool _isLoading = false;
  int _totalOfflineSize = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
    _listenToDownloadProgress();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final regions = _mapService.getAvailableRegions();
      final downloaded = await _mapService.getDownloadedMaps();
      final totalSize = await _mapService.getTotalOfflineSize();

      setState(() {
        _availableRegions = regions;
        _downloadedMaps = downloaded;
        _totalOfflineSize = totalSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('Chyba při načítání dat: $e');
    }
  }

  void _listenToDownloadProgress() {
    _mapService.downloadProgress.listen((progress) {
      setState(() {
        _downloadProgress[progress.regionId] = progress;
      });

      if (progress.status == DownloadStatus.completed) {
        _loadData(); // Refresh po dokončení
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStorageInfo(),
                    const SizedBox(height: 24),
                    _buildDownloadedMaps(),
                    const SizedBox(height: 24),
                    _buildAvailableRegions(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildStorageInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Úložiště offline map',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Využito',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      MapUtils.formatFileSize(_totalOfflineSize),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Stažené mapy',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      '${_downloadedMaps.length}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cleanupOldMaps,
                    icon: const Icon(Icons.cleaning_services),
                    label: const Text('Vyčistit staré'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showSettings,
                    icon: const Icon(Icons.settings),
                    label: const Text('Nastavení'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadedMaps() {
    if (_downloadedMaps.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.map_outlined, size: 48, color: Colors.grey.shade400),
              const SizedBox(height: 8),
              Text(
                'Žádné offline mapy',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 4),
              Text(
                'Stáhněte si mapy pro použití bez internetu',
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Stažené mapy',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _downloadedMaps.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final map = _downloadedMaps[index];
                return _buildDownloadedMapTile(map);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadedMapTile(OfflineMapData map) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: map.isComplete ? Colors.green : Colors.orange,
        child: Icon(
          map.isComplete ? Icons.check : Icons.download,
          color: Colors.white,
        ),
      ),
      title: Text(map.regionName),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Velikost: ${MapUtils.formatFileSize(map.sizeBytes)}'),
          Text('Staženo: ${_formatDate(map.downloadedAt)}'),
          if (!map.isComplete)
            Text(
              'Stahování...',
              style: TextStyle(color: Colors.orange.shade700),
            ),
        ],
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (action) => _handleMapAction(map, action),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'update',
            child: ListTile(
              leading: Icon(Icons.refresh),
              title: Text('Aktualizovat'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('Smazat'),
              dense: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableRegions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dostupné regiony',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _availableRegions.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final region = _availableRegions[index];
                return _buildRegionTile(region);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegionTile(MapRegion region) {
    final isDownloaded = _downloadedMaps.any(
      (map) => map.regionId == region.id,
    );
    final progress = _downloadProgress[region.id];
    final isDownloading =
        progress != null && progress.status == DownloadStatus.downloading;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getPriorityColor(region.priority),
        child: Icon(_getRegionIcon(region.type), color: Colors.white),
      ),
      title: Text(region.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Velikost: ${region.estimatedSize.toStringAsFixed(1)} MB'),
          Text('Priorita: ${_getPriorityText(region.priority)}'),
          if (isDownloading) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(value: progress.progress),
            const SizedBox(height: 4),
            Text(
              '${(progress.progress * 100).toStringAsFixed(0)}% - ${progress.downloadedTiles}/${progress.totalTiles} tiles',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ],
      ),
      trailing: isDownloading
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : isDownloaded
          ? const Icon(Icons.check_circle, color: Colors.green)
          : IconButton(
              onPressed: () => _downloadRegion(region),
              icon: const Icon(Icons.download),
            ),
    );
  }

  Color _getPriorityColor(MapPriority priority) {
    switch (priority) {
      case MapPriority.high:
        return Colors.red;
      case MapPriority.medium:
        return Colors.orange;
      case MapPriority.low:
        return Colors.blue;
    }
  }

  IconData _getRegionIcon(MapType type) {
    switch (type) {
      case MapType.city:
        return Icons.location_city;
      case MapType.region:
        return Icons.landscape;
      case MapType.country:
        return Icons.public;
    }
  }

  String _getPriorityText(MapPriority priority) {
    switch (priority) {
      case MapPriority.high:
        return 'Vysoká';
      case MapPriority.medium:
        return 'Střední';
      case MapPriority.low:
        return 'Nízká';
    }
  }

  Future<void> _downloadRegion(MapRegion region) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Stáhnout ${region.name}?'),
        content: Text(
          'Mapa bude mít přibližně ${region.estimatedSize.toStringAsFixed(1)} MB. '
          'Doporučujeme stahovat pouze přes WiFi.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Stáhnout'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _mapService.downloadRegion(region.id);
      if (!success && mounted) {
        _showError('Nepodařilo se spustit stahování');
      }
    }
  }

  Future<void> _handleMapAction(OfflineMapData map, String action) async {
    switch (action) {
      case 'update':
        await _mapService.downloadRegion(map.regionId, forceUpdate: true);
        break;
      case 'delete':
        final confirmed = await _confirmDelete(map.regionName);
        if (confirmed) {
          await _mapService.deleteOfflineMap(map.regionId);
          _loadData();
        }
        break;
    }
  }

  Future<bool> _confirmDelete(String regionName) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Smazat offline mapu?'),
        content: Text('Opravdu chcete smazat mapu "$regionName"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Smazat'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  Future<void> _cleanupOldMaps() async {
    await _mapService.cleanupOldMaps();
    _loadData();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Staré mapy byly vyčištěny')),
      );
    }
  }

  void _showSettings() {
    // Implementace nastavení offline map
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nastavení offline map'),
        content: const Text('Zde budou nastavení pro offline mapy.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year}';
  }
}
