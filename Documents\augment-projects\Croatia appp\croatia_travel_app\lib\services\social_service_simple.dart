import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/social_simple.dart';
import '../models/transport.dart';

class SocialService {
  static final SocialService _instance = SocialService._internal();
  factory SocialService() => _instance;
  SocialService._internal();

  // Cache pro sociální data
  final Map<String, List<RouteReview>> _reviewsCache = {};
  final Map<String, SharedRoute> _sharedRoutesCache = {};

  /// Sdílení trasy
  Future<SharedRoute?> shareRoute({
    required TransportRoute route,
    required String title,
    String? description,
    List<String> tags = const [],
    SharePrivacy privacy = SharePrivacy.public,
    bool allowComments = true,
    bool allowRatings = true,
  }) async {
    try {
      final sharedRoute = SharedRoute(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        routeId: route.id,
        route: route,
        title: title,
        description: description,
        authorId: await _getCurrentUserId(),
        authorName: await _getCurrentUserName(),
        tags: tags,
        privacy: privacy,
        allowComments: allowComments,
        allowRatings: allowRatings,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        viewCount: 0,
        likeCount: 0,
        shareCount: 0,
        averageRating: 0.0,
        reviewCount: 0,
        isBookmarked: false,
        isLiked: false,
      );

      // Pro demo - simulace úspěšného uložení
      _sharedRoutesCache[sharedRoute.id] = sharedRoute;
      return sharedRoute;
    } catch (e) {
      debugPrint('Chyba při sdílení trasy: $e');
      return null;
    }
  }

  /// Získání sdílených tras
  Future<List<SharedRoute>> getSharedRoutes({
    int page = 0,
    int limit = 20,
    String? searchQuery,
    List<String> tags = const [],
    ShareSortBy sortBy = ShareSortBy.newest,
    SharePrivacy? privacy,
  }) async {
    // Pro demo - vrátíme mock data
    return _generateMockSharedRoutes(limit);
  }

  /// Získání populárních tras
  Future<List<SharedRoute>> getPopularRoutes({int limit = 10}) async {
    return getSharedRoutes(limit: limit, sortBy: ShareSortBy.mostLiked);
  }

  /// Získání tras od přátel
  Future<List<SharedRoute>> getFriendsRoutes({int limit = 20}) async {
    return _generateMockSharedRoutes(limit);
  }

  /// Lajkování trasy
  Future<bool> likeRoute(String routeId) async {
    try {
      // Aktualizace cache
      final route = _sharedRoutesCache[routeId];
      if (route != null) {
        _sharedRoutesCache[routeId] = route.copyWith(
          isLiked: true,
          likeCount: route.likeCount + 1,
        );
      }
      return true;
    } catch (e) {
      debugPrint('Chyba při lajkování: $e');
      return false;
    }
  }

  /// Zrušení lajku
  Future<bool> unlikeRoute(String routeId) async {
    try {
      // Aktualizace cache
      final route = _sharedRoutesCache[routeId];
      if (route != null) {
        _sharedRoutesCache[routeId] = route.copyWith(
          isLiked: false,
          likeCount: route.likeCount - 1,
        );
      }
      return true;
    } catch (e) {
      debugPrint('Chyba při rušení lajku: $e');
      return false;
    }
  }

  /// Přidání do záložek
  Future<bool> bookmarkRoute(String routeId) async {
    try {
      // Aktualizace cache
      final route = _sharedRoutesCache[routeId];
      if (route != null) {
        _sharedRoutesCache[routeId] = route.copyWith(isBookmarked: true);
      }
      return true;
    } catch (e) {
      debugPrint('Chyba při přidávání do záložek: $e');
      return false;
    }
  }

  /// Hodnocení trasy
  Future<RouteReview?> rateRoute({
    required String routeId,
    required double rating,
    String? comment,
    List<String> pros = const [],
    List<String> cons = const [],
    Map<String, double> categoryRatings = const {},
  }) async {
    try {
      final review = RouteReview(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        routeId: routeId,
        userId: await _getCurrentUserId(),
        userName: await _getCurrentUserName(),
        rating: rating,
        comment: comment,
        pros: pros,
        cons: cons,
        categoryRatings: categoryRatings,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        helpfulCount: 0,
        isHelpful: false,
        isVerified: false,
      );

      // Aktualizace cache
      if (!_reviewsCache.containsKey(routeId)) {
        _reviewsCache[routeId] = [];
      }
      _reviewsCache[routeId]!.add(review);
      
      return review;
    } catch (e) {
      debugPrint('Chyba při hodnocení trasy: $e');
      return null;
    }
  }

  /// Získání hodnocení trasy
  Future<List<RouteReview>> getRouteReviews(
    String routeId, {
    int page = 0,
    int limit = 20,
    ReviewSortBy sortBy = ReviewSortBy.newest,
  }) async {
    // Nejdříve zkusit cache
    if (_reviewsCache.containsKey(routeId)) {
      return _reviewsCache[routeId]!;
    }

    // Pro demo - vrátíme prázdný seznam
    return [];
  }

  /// Označení hodnocení jako užitečné
  Future<bool> markReviewHelpful(String reviewId) async {
    return true; // Mock implementation
  }

  /// Nahlášení nevhodného obsahu
  Future<bool> reportContent({
    required String contentId,
    required ContentType contentType,
    required ReportReason reason,
    String? description,
  }) async {
    return true; // Mock implementation
  }

  /// Sledování uživatele
  Future<bool> followUser(String userId) async {
    return true; // Mock implementation
  }

  /// Získání statistik uživatele
  Future<UserSocialStats?> getUserStats(String userId) async {
    return null; // Mock implementation
  }

  /// Získání doporučených tras
  Future<List<SharedRoute>> getRecommendedRoutes({int limit = 10}) async {
    return _generateMockSharedRoutes(limit);
  }

  /// Vyhledávání tras podle lokace
  Future<List<SharedRoute>> searchRoutesByLocation({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
    int limit = 20,
  }) async {
    return _generateMockSharedRoutes(limit);
  }

  /// Generování mock dat
  List<SharedRoute> _generateMockSharedRoutes(int count) {
    final routes = <SharedRoute>[];
    
    for (int i = 0; i < count; i++) {
      final route = SharedRoute(
        id: 'shared_route_$i',
        routeId: 'route_$i',
        route: _generateMockTransportRoute(i),
        title: 'Trasa ${i + 1}: ${_getMockDestination(i)}',
        description: 'Skvělá trasa pro ${_getMockDescription(i)}',
        authorId: 'user_$i',
        authorName: _getMockAuthorName(i),
        tags: _getMockTags(i),
        privacy: SharePrivacy.public,
        allowComments: true,
        allowRatings: true,
        createdAt: DateTime.now().subtract(Duration(days: i)),
        updatedAt: DateTime.now().subtract(Duration(days: i)),
        viewCount: 100 + i * 50,
        likeCount: 10 + i * 5,
        shareCount: i * 2,
        averageRating: 3.5 + (i % 3) * 0.5,
        reviewCount: i * 3,
        isBookmarked: i % 4 == 0,
        isLiked: i % 3 == 0,
      );
      routes.add(route);
    }
    
    return routes;
  }

  TransportRoute _generateMockTransportRoute(int index) {
    return TransportRoute(
      id: 'route_$index',
      fromStopId: 'stop_from_$index',
      toStopId: 'stop_to_$index',
      segments: [],
      totalDuration: Duration(minutes: 20 + index * 5),
      totalDistance: 5000 + index * 1000,
      totalPrice: 15 + index * 5,
      currency: 'HRK',
      departureTime: DateTime.now(),
      arrivalTime: DateTime.now().add(Duration(minutes: 20 + index * 5)),
      transfers: index % 3,
      routeType: RouteType.fastest,
    );
  }

  String _getMockDestination(int index) {
    final destinations = ['Centrum', 'Pláž', 'Muzeum', 'Park', 'Nádraží'];
    return destinations[index % destinations.length];
  }

  String _getMockDescription(int index) {
    final descriptions = ['turisty', 'místní', 'rodiny', 'studenty', 'seniory'];
    return descriptions[index % descriptions.length];
  }

  String _getMockAuthorName(int index) {
    final names = ['Marko', 'Ana', 'Petar', 'Maja', 'Luka'];
    return names[index % names.length];
  }

  List<String> _getMockTags(int index) {
    final allTags = ['rychlé', 'levné', 'pohodlné', 'turistické', 'místní'];
    return [allTags[index % allTags.length], allTags[(index + 1) % allTags.length]];
  }

  // Utility metody
  Future<String> _getCurrentUserId() async {
    return 'user_123'; // Mock
  }

  Future<String> _getCurrentUserName() async {
    return 'Testovací uživatel'; // Mock
  }

  /// Vyčištění cache
  void clearCache() {
    _reviewsCache.clear();
    _sharedRoutesCache.clear();
  }

  /// Dispose
  void dispose() {
    clearCache();
  }
}
