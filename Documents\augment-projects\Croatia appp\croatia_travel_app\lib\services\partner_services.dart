import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/transport_simple.dart';

/// Služba pro integraci s partnerskými dopravními službami
class PartnerServices {
  static final PartnerServices _instance = PartnerServices._internal();
  factory PartnerServices() => _instance;
  PartnerServices._internal();

  final Dio _dio = Dio();

  /// Inicializace služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent': 'CroatiaTravel/1.0 (Partner Integration)',
        'Accept': 'application/json',
      },
    );
  }

  /// 1. GOOGLE TRANSIT PARTNER API
  /// Získán<PERSON> dat přes Google Transit Partner API
  Future<List<TransportRoute>> getGoogleTransitRoutes({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
  }) async {
    try {
      // Google Transit Partner API (vyžaduje speciální partnerství)
      final response = await _dio.get(
        'https://maps.googleapis.com/maps/api/directions/json',
        queryParameters: {
          'origin': '$fromLat,$fromLng',
          'destination': '$toLat,$toLng',
          'mode': 'transit',
          'departure_time':
              departureTime?.millisecondsSinceEpoch ??
              DateTime.now().millisecondsSinceEpoch,
          'key': 'YOUR_GOOGLE_MAPS_API_KEY',
          'region': 'hr', // Chorvatsko
          'language': 'hr',
        },
      );

      if (response.statusCode == 200) {
        return _parseGoogleTransitResponse(response.data);
      }
    } catch (e) {
      debugPrint('Google Transit API chyba: $e');
    }

    return [];
  }

  /// 2. MOOVIT PARTNER API
  /// Integrace s Moovit (pokud mají data pro Chorvatsko)
  Future<List<TransportStop>> getMoovitStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
  }) async {
    try {
      // Moovit Partner API
      final response = await _dio.get(
        'https://partners.moovit.com/api/v1/stops/nearby',
        queryParameters: {
          'lat': latitude,
          'lon': longitude,
          'radius': radiusKm * 1000, // v metrech
          'api_key': 'YOUR_MOOVIT_API_KEY',
          'country': 'HR',
        },
        options: Options(
          headers: {'Authorization': 'Bearer YOUR_MOOVIT_TOKEN'},
        ),
      );

      if (response.statusCode == 200) {
        return _parseMoovitStopsResponse(response.data);
      }
    } catch (e) {
      debugPrint('Moovit API chyba: $e');
    }

    return [];
  }

  /// 3. CITYMAPPER INTEGRATION
  /// Integrace s Citymapper API
  Future<List<TransportRoute>> getCitymapperRoutes({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
  }) async {
    try {
      // Citymapper API
      final response = await _dio.get(
        'https://developer.citymapper.com/api/1/traveltime/',
        queryParameters: {
          'startcoord': '$fromLat,$fromLng',
          'endcoord': '$toLat,$toLng',
          'time': DateTime.now().toIso8601String(),
          'time_type': 'departure',
        },
        options: Options(
          headers: {'Citymapper-Partner-Key': 'YOUR_CITYMAPPER_KEY'},
        ),
      );

      if (response.statusCode == 200) {
        return _parseCitymapperResponse(response.data);
      }
    } catch (e) {
      debugPrint('Citymapper API chyba: $e');
    }

    return [];
  }

  /// 4. TRANSITLAND API (Open Source)
  /// Transitland je open source databáze GTFS dat
  Future<List<TransportStop>> getTransitlandStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
  }) async {
    try {
      final response = await _dio.get(
        'https://transit.land/api/v2/stops',
        queryParameters: {
          'lat': latitude,
          'lon': longitude,
          'radius': radiusKm * 1000,
          'country_code': 'HR',
          'served_by_vehicle_type': '3', // Bus
        },
      );

      if (response.statusCode == 200) {
        return _parseTransitlandStopsResponse(response.data);
      }
    } catch (e) {
      debugPrint('Transitland API chyba: $e');
    }

    return [];
  }

  /// 5. OPENSTREETMAP OVERPASS API
  /// Získání dopravních dat z OpenStreetMap
  Future<List<TransportStop>> getOSMTransportStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
  }) async {
    try {
      final query =
          '''
        [out:json][timeout:25];
        (
          node["public_transport"="stop_position"](around:${radiusKm * 1000},$latitude,$longitude);
          node["highway"="bus_stop"](around:${radiusKm * 1000},$latitude,$longitude);
          node["railway"="station"](around:${radiusKm * 1000},$latitude,$longitude);
          node["amenity"="ferry_terminal"](around:${radiusKm * 1000},$latitude,$longitude);
        );
        out body;
      ''';

      final response = await _dio.post(
        'https://overpass-api.de/api/interpreter',
        data: query,
        options: Options(headers: {'Content-Type': 'text/plain'}),
      );

      if (response.statusCode == 200) {
        return _parseOSMResponse(response.data);
      }
    } catch (e) {
      debugPrint('OSM Overpass API chyba: $e');
    }

    return [];
  }

  /// 6. ROME2RIO API
  /// Rome2Rio pro mezinárodní spojení
  Future<List<TransportRoute>> getRome2RioRoutes({
    required String fromCity,
    required String toCity,
    DateTime? departureDate,
  }) async {
    try {
      final response = await _dio.get(
        'https://free.rome2rio.com/api/1.4/json/Search',
        queryParameters: {
          'key': 'YOUR_ROME2RIO_KEY',
          'oName': fromCity,
          'dName': toCity,
          'oKind': 'city',
          'dKind': 'city',
          'currency': 'HRK',
          'lang': 'hr',
        },
      );

      if (response.statusCode == 200) {
        return _parseRome2RioResponse(response.data);
      }
    } catch (e) {
      debugPrint('Rome2Rio API chyba: $e');
    }

    return [];
  }

  /// Parsing metody pro různé API

  List<TransportRoute> _parseGoogleTransitResponse(Map<String, dynamic> data) {
    final routes = <TransportRoute>[];

    try {
      final routesData = data['routes'] as List? ?? [];

      for (final routeData in routesData) {
        final legs = routeData['legs'] as List? ?? [];

        for (final leg in legs) {
          final steps = leg['steps'] as List? ?? [];
          final segments = <RouteSegment>[];

          for (final step in steps) {
            if (step['travel_mode'] == 'TRANSIT') {
              final transitDetails = step['transit_details'];

              segments.add(
                RouteSegment(
                  type: _parseGoogleTransitType(
                    transitDetails['line']['vehicle']['type'],
                  ),
                  routeNumber: transitDetails['line']['short_name'] ?? '',
                  fromStopName: transitDetails['departure_stop']['name'] ?? '',
                  toStopName: transitDetails['arrival_stop']['name'] ?? '',
                  duration: Duration(seconds: step['duration']['value']),
                  distance: step['distance']['value'],
                  instructions: step['html_instructions'] ?? '',
                ),
              );
            }
          }

          if (segments.isNotEmpty) {
            routes.add(
              TransportRoute(
                id: 'google_${DateTime.now().millisecondsSinceEpoch}',
                fromStopId: 'google_from',
                toStopId: 'google_to',
                segments: segments,
                totalDuration: Duration(seconds: leg['duration']['value']),
                totalDistance: leg['distance']['value'],
                totalPrice: 0.0, // Google nevrací ceny
                currency: 'HRK',
                departureTime: DateTime.now(),
                arrivalTime: DateTime.now().add(
                  Duration(seconds: leg['duration']['value']),
                ),
                transfers: segments.length - 1,
                routeType: RouteType.fastest,
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Chyba při parsování Google Transit: $e');
    }

    return routes;
  }

  List<TransportStop> _parseMoovitStopsResponse(Map<String, dynamic> data) {
    final stops = <TransportStop>[];

    try {
      final stopsData = data['stops'] as List? ?? [];

      for (final stopData in stopsData) {
        stops.add(
          TransportStop(
            id: 'moovit_${stopData['id']}',
            name: stopData['name'] ?? '',
            latitude: stopData['lat']?.toDouble() ?? 0.0,
            longitude: stopData['lon']?.toDouble() ?? 0.0,
            city: stopData['city'] ?? '',
            platforms: [],
            facilities: [],
            isAccessible: stopData['wheelchair_accessible'] ?? false,
          ),
        );
      }
    } catch (e) {
      debugPrint('Chyba při parsování Moovit: $e');
    }

    return stops;
  }

  List<TransportRoute> _parseCitymapperResponse(Map<String, dynamic> data) {
    // Implementace parsování Citymapper odpovědi
    return [];
  }

  List<TransportStop> _parseTransitlandStopsResponse(
    Map<String, dynamic> data,
  ) {
    final stops = <TransportStop>[];

    try {
      final stopsData = data['stops'] as List? ?? [];

      for (final stopData in stopsData) {
        final geometry = stopData['geometry'];
        final coordinates = geometry['coordinates'] as List;

        stops.add(
          TransportStop(
            id: 'transitland_${stopData['onestop_id']}',
            name: stopData['stop_name'] ?? '',
            latitude: coordinates[1]?.toDouble() ?? 0.0,
            longitude: coordinates[0]?.toDouble() ?? 0.0,
            city: stopData['stop_timezone']?.split('/').last ?? '',
            platforms: [],
            facilities: [],
            isAccessible: stopData['wheelchair_boarding'] == 1,
          ),
        );
      }
    } catch (e) {
      debugPrint('Chyba při parsování Transitland: $e');
    }

    return stops;
  }

  List<TransportStop> _parseOSMResponse(Map<String, dynamic> data) {
    final stops = <TransportStop>[];

    try {
      final elements = data['elements'] as List? ?? [];

      for (final element in elements) {
        final tags = element['tags'] as Map<String, dynamic>? ?? {};

        stops.add(
          TransportStop(
            id: 'osm_${element['id']}',
            name: tags['name'] ?? tags['ref'] ?? 'Neznámá zastávka',
            latitude: element['lat']?.toDouble() ?? 0.0,
            longitude: element['lon']?.toDouble() ?? 0.0,
            city: tags['addr:city'] ?? '',
            platforms: [],
            facilities: _parseOSMFacilities(tags),
            isAccessible: tags['wheelchair'] == 'yes',
          ),
        );
      }
    } catch (e) {
      debugPrint('Chyba při parsování OSM: $e');
    }

    return stops;
  }

  List<TransportRoute> _parseRome2RioResponse(Map<String, dynamic> data) {
    // Implementace parsování Rome2Rio odpovědi
    return [];
  }

  /// Pomocné metody
  SegmentType _parseGoogleTransitType(String? vehicleType) {
    switch (vehicleType?.toLowerCase()) {
      case 'bus':
        return SegmentType.bus;
      case 'tram':
        return SegmentType.tram;
      case 'subway':
        return SegmentType.metro;
      case 'rail':
        return SegmentType.train;
      case 'ferry':
        return SegmentType.ferry;
      default:
        return SegmentType.bus;
    }
  }

  List<String> _parseOSMFacilities(Map<String, dynamic> tags) {
    final facilities = <String>[];

    if (tags['shelter'] == 'yes') facilities.add('shelter');
    if (tags['bench'] == 'yes') facilities.add('bench');
    if (tags['tactile_paving'] == 'yes') facilities.add('tactile_paving');

    return facilities;
  }

  /// Kombinovaná metoda pro získání nejlepších dat
  Future<List<TransportStop>> getBestAvailableStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
  }) async {
    final allStops = <TransportStop>[];

    // Paralelní volání všech dostupných API
    final results = await Future.wait([
      getOSMTransportStops(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      ),
      getTransitlandStops(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      ),
      getMoovitStops(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      ),
    ], eagerError: false);

    // Sloučení výsledků
    for (final result in results) {
      allStops.addAll(result);
    }

    // Deduplikace na základě vzdálenosti
    return _deduplicateStops(allStops);
  }

  List<TransportStop> _deduplicateStops(List<TransportStop> stops) {
    final deduplicated = <TransportStop>[];

    for (final stop in stops) {
      bool isDuplicate = false;

      for (final existing in deduplicated) {
        final distance = _calculateDistance(
          stop.latitude,
          stop.longitude,
          existing.latitude,
          existing.longitude,
        );

        // Pokud jsou zastávky blíž než 50 metrů, považujeme je za duplicitní
        if (distance < 50) {
          isDuplicate = true;
          break;
        }
      }

      if (!isDuplicate) {
        deduplicated.add(stop);
      }
    }

    return deduplicated;
  }

  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    // Haversine formula pro výpočet vzdálenosti
    const double earthRadius = 6371000; // metry

    final dLat = (lat2 - lat1) * (3.14159 / 180);
    final dLon = (lon2 - lon1) * (3.14159 / 180);

    final a =
        (dLat / 2).sin() * (dLat / 2).sin() +
        lat1 *
            (3.14159 / 180).cos() *
            lat2 *
            (3.14159 / 180).cos() *
            (dLon / 2).sin() *
            (dLon / 2).sin();

    final c = 2 * a.sqrt().asin();

    return earthRadius * c;
  }
}

/// Extension pro matematické funkce
extension MathExtension on double {
  double sin() => math.sin(this);
  double cos() => math.cos(this);
  double asin() => math.asin(this);
  double sqrt() => math.sqrt(this);
}
