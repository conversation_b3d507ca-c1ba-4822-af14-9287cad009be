import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:archive/archive.dart';
import '../models/transport_simple.dart';

/// Služba pro získávání dat z oficiálních Open Data zdrojů
class OpenDataService {
  static final OpenDataService _instance = OpenDataService._internal();
  factory OpenDataService() => _instance;
  OpenDataService._internal();

  final Dio _dio = Dio();
  final Map<String, dynamic> _cache = {};

  // Oficiální Open Data endpointy
  static const Map<String, OpenDataConfig> _configs = {
    'croatia': OpenDataConfig(
      name: 'Croatian Open Data Portal',
      baseUrl: 'https://data.gov.hr/api/3',
      gtfsUrl: 'https://data.gov.hr/dataset/gtfs-zagreb',
      isOfficial: true,
      updateFrequency: 'daily',
    ),
    'zagreb': OpenDataConfig(
      name: 'Zagreb Open Data',
      baseUrl: 'https://data.zagreb.hr/api/3',
      gtfsUrl: 'https://data.zagreb.hr/dataset/zet-gtfs',
      isOfficial: true,
      updateFrequency: 'daily',
    ),
    'eu_transport': OpenDataConfig(
      name: 'EU Transport Data',
      baseUrl: 'https://transport.data.gov.eu/api',
      gtfsUrl: 'https://transport.data.gov.eu/croatia',
      isOfficial: true,
      updateFrequency: 'weekly',
    ),
  };

  /// Inicializace služby
  Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(minutes: 5), // GTFS soubory mohou být velké
      headers: {
        'User-Agent': 'CroatiaTravel/1.0 (Open Data Consumer)',
        'Accept': 'application/json, application/zip',
      },
    );

    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: false,
        responseBody: false,
        logPrint: (obj) => debugPrint('[Open Data] $obj'),
      ));
    }
  }

  /// Získání GTFS dat pro město
  Future<GTFSData?> getGTFSData(String cityId) async {
    try {
      debugPrint('📊 Načítám GTFS data pro $cityId...');
      
      final config = _getConfigForCity(cityId);
      if (config == null) {
        debugPrint('❌ Žádná Open Data konfigurace pro $cityId');
        return null;
      }

      // 1. Získání metadat o datasetu
      final datasetInfo = await _getDatasetInfo(config);
      if (datasetInfo == null) {
        debugPrint('❌ Dataset info nenalezen');
        return null;
      }

      // 2. Stažení GTFS souboru
      final gtfsZipUrl = datasetInfo['download_url'];
      if (gtfsZipUrl == null) {
        debugPrint('❌ GTFS download URL nenalezen');
        return null;
      }

      final gtfsData = await _downloadAndParseGTFS(gtfsZipUrl);
      if (gtfsData != null) {
        debugPrint('✅ GTFS data úspěšně načtena: ${gtfsData.stops.length} zastávek');
      }

      return gtfsData;
    } catch (e) {
      debugPrint('❌ Chyba při načítání GTFS dat: $e');
      return null;
    }
  }

  /// Získání zastávek z Open Data
  Future<List<TransportStop>> getStopsFromOpenData(String cityId) async {
    final gtfsData = await getGTFSData(cityId);
    if (gtfsData == null) {
      return [];
    }

    return gtfsData.stops.map((gtfsStop) => TransportStop(
      id: gtfsStop.stopId,
      name: gtfsStop.stopName,
      latitude: gtfsStop.stopLat,
      longitude: gtfsStop.stopLon,
      city: cityId,
      zone: gtfsStop.zoneId,
      platforms: [],
      facilities: _parseFacilities(gtfsStop.wheelchairBoarding),
      isAccessible: gtfsStop.wheelchairBoarding == '1',
      hasRealTimeInfo: false, // Open Data obvykle nemají real-time
    )).toList();
  }

  /// Získání tras z Open Data
  Future<List<PublicTransport>> getRoutesFromOpenData(String cityId) async {
    final gtfsData = await getGTFSData(cityId);
    if (gtfsData == null) {
      return [];
    }

    return gtfsData.routes.map((gtfsRoute) => PublicTransport(
      id: gtfsRoute.routeId,
      city: cityId,
      type: _parseTransportType(gtfsRoute.routeType),
      routeNumber: gtfsRoute.routeShortName,
      routeName: gtfsRoute.routeLongName,
      direction: '', // Bude doplněno z trips
      stops: [], // Bude doplněno z stop_times
      operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], // Default
      startTime: '05:00',
      endTime: '23:00',
      frequency: 15, // Default
      price: _getDefaultPrice(cityId),
      currency: 'HRK',
      isActive: true,
      lastUpdated: DateTime.now(),
    )).toList();
  }

  /// Získání informací o datasetu
  Future<Map<String, dynamic>?> _getDatasetInfo(OpenDataConfig config) async {
    try {
      final response = await _dio.get('${config.baseUrl}/action/package_show', 
        queryParameters: {
          'id': _extractDatasetId(config.gtfsUrl),
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          final resources = data['result']['resources'] as List;
          
          // Hledáme GTFS soubor (obvykle ZIP)
          for (final resource in resources) {
            final format = resource['format']?.toString().toLowerCase();
            if (format == 'zip' || format == 'gtfs') {
              return {
                'download_url': resource['url'],
                'last_modified': resource['last_modified'],
                'size': resource['size'],
                'format': format,
              };
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Chyba při získávání dataset info: $e');
    }
    
    return null;
  }

  /// Stažení a parsování GTFS souboru
  Future<GTFSData?> _downloadAndParseGTFS(String zipUrl) async {
    try {
      debugPrint('⬇️ Stahuji GTFS soubor: $zipUrl');
      
      final response = await _dio.get(
        zipUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode != 200) {
        debugPrint('❌ Chyba při stahování GTFS: ${response.statusCode}');
        return null;
      }

      // Rozbalení ZIP souboru
      final archive = ZipDecoder().decodeBytes(response.data);
      
      final gtfsData = GTFSData();

      // Parsování jednotlivých GTFS souborů
      for (final file in archive) {
        if (file.isFile) {
          final content = utf8.decode(file.content);
          
          switch (file.name) {
            case 'stops.txt':
              gtfsData.stops = _parseStopsTxt(content);
              break;
            case 'routes.txt':
              gtfsData.routes = _parseRoutesTxt(content);
              break;
            case 'trips.txt':
              gtfsData.trips = _parseTripsTxt(content);
              break;
            case 'stop_times.txt':
              gtfsData.stopTimes = _parseStopTimesTxt(content);
              break;
            case 'calendar.txt':
              gtfsData.calendar = _parseCalendarTxt(content);
              break;
          }
        }
      }

      debugPrint('✅ GTFS parsování dokončeno');
      return gtfsData;
    } catch (e) {
      debugPrint('❌ Chyba při parsování GTFS: $e');
      return null;
    }
  }

  /// Parsování stops.txt
  List<GTFSStop> _parseStopsTxt(String content) {
    final stops = <GTFSStop>[];
    final lines = content.split('\n');
    
    if (lines.isEmpty) return stops;
    
    final headers = lines[0].split(',');
    
    for (int i = 1; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty) continue;
      
      final values = _parseCSVLine(line);
      if (values.length >= headers.length) {
        final stop = GTFSStop();
        
        for (int j = 0; j < headers.length; j++) {
          final header = headers[j].trim();
          final value = values[j].trim().replaceAll('"', '');
          
          switch (header) {
            case 'stop_id':
              stop.stopId = value;
              break;
            case 'stop_name':
              stop.stopName = value;
              break;
            case 'stop_lat':
              stop.stopLat = double.tryParse(value) ?? 0.0;
              break;
            case 'stop_lon':
              stop.stopLon = double.tryParse(value) ?? 0.0;
              break;
            case 'zone_id':
              stop.zoneId = value;
              break;
            case 'wheelchair_boarding':
              stop.wheelchairBoarding = value;
              break;
          }
        }
        
        if (stop.stopId.isNotEmpty && stop.stopName.isNotEmpty) {
          stops.add(stop);
        }
      }
    }
    
    return stops;
  }

  /// Parsování routes.txt
  List<GTFSRoute> _parseRoutesTxt(String content) {
    final routes = <GTFSRoute>[];
    final lines = content.split('\n');
    
    if (lines.isEmpty) return routes;
    
    final headers = lines[0].split(',');
    
    for (int i = 1; i < lines.length; i++) {
      final line = lines[i].trim();
      if (line.isEmpty) continue;
      
      final values = _parseCSVLine(line);
      if (values.length >= headers.length) {
        final route = GTFSRoute();
        
        for (int j = 0; j < headers.length; j++) {
          final header = headers[j].trim();
          final value = values[j].trim().replaceAll('"', '');
          
          switch (header) {
            case 'route_id':
              route.routeId = value;
              break;
            case 'route_short_name':
              route.routeShortName = value;
              break;
            case 'route_long_name':
              route.routeLongName = value;
              break;
            case 'route_type':
              route.routeType = int.tryParse(value) ?? 3; // 3 = bus
              break;
          }
        }
        
        if (route.routeId.isNotEmpty) {
          routes.add(route);
        }
      }
    }
    
    return routes;
  }

  /// Parsování trips.txt (zjednodušeno)
  List<GTFSTrip> _parseTripsTxt(String content) {
    // Implementace podobná jako u stops a routes
    return [];
  }

  /// Parsování stop_times.txt (zjednodušeno)
  List<GTFSStopTime> _parseStopTimesTxt(String content) {
    // Implementace podobná jako u stops a routes
    return [];
  }

  /// Parsování calendar.txt (zjednodušeno)
  List<GTFSCalendar> _parseCalendarTxt(String content) {
    // Implementace podobná jako u stops a routes
    return [];
  }

  /// Parsování CSV řádku s ohledem na uvozovky
  List<String> _parseCSVLine(String line) {
    final values = <String>[];
    final buffer = StringBuffer();
    bool inQuotes = false;
    
    for (int i = 0; i < line.length; i++) {
      final char = line[i];
      
      if (char == '"') {
        inQuotes = !inQuotes;
      } else if (char == ',' && !inQuotes) {
        values.add(buffer.toString());
        buffer.clear();
      } else {
        buffer.write(char);
      }
    }
    
    values.add(buffer.toString());
    return values;
  }

  /// Pomocné metody
  OpenDataConfig? _getConfigForCity(String cityId) {
    switch (cityId.toLowerCase()) {
      case 'zagreb':
        return _configs['zagreb'];
      default:
        return _configs['croatia'];
    }
  }

  String _extractDatasetId(String url) {
    final uri = Uri.parse(url);
    return uri.pathSegments.last;
  }

  List<String> _parseFacilities(String wheelchairBoarding) {
    final facilities = <String>[];
    if (wheelchairBoarding == '1') {
      facilities.add('wheelchair');
    }
    return facilities;
  }

  TransportType _parseTransportType(int routeType) {
    // GTFS route types
    switch (routeType) {
      case 0: return TransportType.tram;
      case 1: return TransportType.metro;
      case 2: return TransportType.train;
      case 3: return TransportType.bus;
      case 4: return TransportType.ferry;
      default: return TransportType.bus;
    }
  }

  double _getDefaultPrice(String cityId) {
    switch (cityId.toLowerCase()) {
      case 'zagreb': return 4.0;
      case 'split': return 11.0;
      case 'rijeka': return 8.0;
      default: return 10.0;
    }
  }

  /// Vyčištění cache
  void clearCache() {
    _cache.clear();
  }
}

/// Konfigurace Open Data zdroje
class OpenDataConfig {
  final String name;
  final String baseUrl;
  final String gtfsUrl;
  final bool isOfficial;
  final String updateFrequency;

  const OpenDataConfig({
    required this.name,
    required this.baseUrl,
    required this.gtfsUrl,
    required this.isOfficial,
    required this.updateFrequency,
  });
}

/// GTFS data struktury
class GTFSData {
  List<GTFSStop> stops = [];
  List<GTFSRoute> routes = [];
  List<GTFSTrip> trips = [];
  List<GTFSStopTime> stopTimes = [];
  List<GTFSCalendar> calendar = [];
}

class GTFSStop {
  String stopId = '';
  String stopName = '';
  double stopLat = 0.0;
  double stopLon = 0.0;
  String zoneId = '';
  String wheelchairBoarding = '';
}

class GTFSRoute {
  String routeId = '';
  String routeShortName = '';
  String routeLongName = '';
  int routeType = 3; // 3 = bus
}

class GTFSTrip {
  String tripId = '';
  String routeId = '';
  String serviceId = '';
  String tripHeadsign = '';
}

class GTFSStopTime {
  String tripId = '';
  String stopId = '';
  String arrivalTime = '';
  String departureTime = '';
  int stopSequence = 0;
}

class GTFSCalendar {
  String serviceId = '';
  bool monday = false;
  bool tuesday = false;
  bool wednesday = false;
  bool thursday = false;
  bool friday = false;
  bool saturday = false;
  bool sunday = false;
}
