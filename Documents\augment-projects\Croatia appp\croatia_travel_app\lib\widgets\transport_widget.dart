import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../models/transport_simple.dart';
import '../services/enhanced_transport_service.dart';

class TransportWidget extends StatefulWidget {
  const TransportWidget({super.key});

  @override
  State<TransportWidget> createState() => _TransportWidgetState();
}

class _TransportWidgetState extends State<TransportWidget> {
  final EnhancedTransportService _transportService = EnhancedTransportService();

  List<TransportStop> _nearbyStops = [];
  List<PublicTransport> _routes = [];
  List<RealTimeArrival> _arrivals = [];
  bool _isLoading = false;
  String? _selectedStopId;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _loadCurrentLocation();
  }

  @override
  void dispose() {
    _transportService.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentLocation() async {
    try {
      setState(() => _isLoading = true);

      _currentPosition = await Geolocator.getCurrentPosition();
      await _loadNearbyStops();
      await _loadRoutes();
    } catch (e) {
      _showError('Chyba při načítání polohy: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadNearbyStops() async {
    if (_currentPosition == null) return;

    try {
      final stops = await _transportService.findNearbyStops(
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        radiusKm: 1.0,
      );

      setState(() => _nearbyStops = stops);
    } catch (e) {
      _showError('Chyba při načítání zastávek: $e');
    }
  }

  Future<void> _loadRoutes() async {
    try {
      final stops = await _transportService.getStopsForCity('zagreb');
      // Extrahujeme linky z zastávek
      final routes = <PublicTransport>[];
      for (final stop in stops) {
        // Mock data pro demonstraci
        routes.add(
          PublicTransport(
            id: 'route_${stop.id}',
            city: 'Zagreb',
            type: TransportType.bus,
            routeNumber: '6',
            routeName: 'Črnomerec - Sopot',
            direction: 'Sopot',
            stops: [stop],
            operatingDays: [
              'monday',
              'tuesday',
              'wednesday',
              'thursday',
              'friday',
            ],
            startTime: '05:00',
            endTime: '23:00',
            frequency: 10,
            price: 4.0,
            currency: 'HRK',
            isActive: true,
            lastUpdated: DateTime.now(),
          ),
        );
      }
      setState(() => _routes = routes.take(5).toList());
    } catch (e) {
      _showError('Chyba při načítání linek: $e');
    }
  }

  Future<void> _loadArrivals(String stopId) async {
    try {
      setState(() => _isLoading = true);

      final arrivals = await _transportService.getRealTimeArrivals(
        stopId,
        'zagreb',
      );
      setState(() {
        _arrivals = arrivals;
        _selectedStopId = stopId;
      });
    } catch (e) {
      _showError('Chyba při načítání příjezdů: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _loadCurrentLocation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildNearbyStops(),
            const SizedBox(height: 24),
            if (_selectedStopId != null) _buildRealTimeArrivals(),
            const SizedBox(height: 24),
            _buildRoutesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rychlé akce',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _planRoute,
                    icon: const Icon(Icons.directions),
                    label: const Text('Naplánovat cestu'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _buyTicket,
                    icon: const Icon(Icons.confirmation_number),
                    label: const Text('Koupit jízdenku'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNearbyStops() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Zastávky v okolí',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_nearbyStops.isEmpty)
              const Text('Žádné zastávky v okolí')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _nearbyStops.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final stop = _nearbyStops[index];
                  return ListTile(
                    leading: const Icon(Icons.directions_bus),
                    title: Text(stop.name),
                    subtitle: Text('${stop.arrivals.length} linek'),
                    trailing: stop.hasRealTimeInfo
                        ? const Icon(Icons.access_time, color: Colors.green)
                        : null,
                    onTap: () => _loadArrivals(stop.id),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRealTimeArrivals() {
    final selectedStop = _nearbyStops.firstWhere(
      (stop) => stop.id == _selectedStopId,
      orElse: () => _nearbyStops.first,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Příjezdy - ${selectedStop.name}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _loadArrivals(_selectedStopId!),
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_arrivals.isEmpty)
              const Text('Žádné příjezdy')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _arrivals.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final arrival = _arrivals[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: arrival.isRealTime
                          ? Colors.green
                          : Colors.grey,
                      child: Text(
                        arrival.routeNumber,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(arrival.direction),
                    subtitle: Text(
                      arrival.delay != null && arrival.delay!.inMinutes > 0
                          ? 'Zpoždění ${arrival.delay!.inMinutes} min'
                          : 'Včas',
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${arrival.timeToArrival.inMinutes} min',
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        if (arrival.isRealTime)
                          const Icon(
                            Icons.live_tv,
                            size: 16,
                            color: Colors.green,
                          ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoutesList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Všechny linky',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_routes.isEmpty)
              const Text('Žádné linky')
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _routes.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final route = _routes[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getTransportTypeColor(route.type),
                      child: Text(
                        route.routeNumber,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(route.routeName),
                    subtitle: Text(
                      '${route.frequency} min • ${route.price} ${route.currency}',
                    ),
                    trailing: Icon(_getTransportTypeIcon(route.type)),
                    onTap: () => _showRouteDetails(route),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Color _getTransportTypeColor(TransportType type) {
    switch (type) {
      case TransportType.bus:
        return Colors.blue;
      case TransportType.tram:
        return Colors.green;
      case TransportType.metro:
        return Colors.red;
      case TransportType.train:
        return Colors.purple;
      case TransportType.ferry:
        return Colors.cyan;
      case TransportType.trolleybus:
        return Colors.orange;
    }
  }

  IconData _getTransportTypeIcon(TransportType type) {
    switch (type) {
      case TransportType.bus:
        return Icons.directions_bus;
      case TransportType.tram:
        return Icons.tram;
      case TransportType.metro:
        return Icons.subway;
      case TransportType.train:
        return Icons.train;
      case TransportType.ferry:
        return Icons.directions_boat;
      case TransportType.trolleybus:
        return Icons.directions_bus;
    }
  }

  void _planRoute() {
    // Implementace plánování trasy
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Otevírám plánovač tras...')));
  }

  void _buyTicket() {
    // Implementace nákupu jízdenky
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Otevírám nákup jízdenek...')));
  }

  void _showRouteDetails(PublicTransport route) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Linka ${route.routeNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Název: ${route.routeName}'),
            Text('Směr: ${route.direction}'),
            Text('Frekvence: ${route.frequency} minut'),
            Text('Cena: ${route.price} ${route.currency}'),
            Text('Provoz: ${route.startTime} - ${route.endTime}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zavřít'),
          ),
        ],
      ),
    );
  }
}
