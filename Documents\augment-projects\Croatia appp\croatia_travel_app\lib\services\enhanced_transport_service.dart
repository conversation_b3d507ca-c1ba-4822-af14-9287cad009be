import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../models/transport_simple.dart';
import '../config/api_config.dart' as config;
import 'real_transport_api_service.dart';

/// Rozšířený transport service s reálnými API integracemi
class EnhancedTransportService {
  static final EnhancedTransportService _instance =
      EnhancedTransportService._internal();
  factory EnhancedTransportService() => _instance;
  EnhancedTransportService._internal();

  final Dio _dio = Dio();
  final RealTransportApiService _realApi = RealTransportApiService();

  // Cache pro optimalizaci výkonu
  final Map<String, List<TransportStop>> _stopsCache = {};
  final Map<String, List<RealTimeArrival>> _realTimeCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};

  /// Inicializace služby
  Future<void> initialize() async {
    await _realApi.initialize();

    _dio.options = BaseOptions(
      connectTimeout: config.ApiConfig.requestTimeout,
      receiveTimeout: config.ApiConfig.requestTimeout,
      headers: config.ApiConfig.getApiHeaders(''),
    );

    // Rate limiting interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          await _checkRateLimit();
          handler.next(options);
        },
      ),
    );

    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: false,
          logPrint: (obj) => debugPrint('[API] $obj'),
        ),
      );
    }
  }

  /// Získání zastávek pro město s real-time daty
  Future<List<TransportStop>> getStopsForCity(String cityId) async {
    final cacheKey = 'stops_$cityId';

    // Kontrola cache
    if (_isDataCached(cacheKey, config.ApiConfig.staticDataCacheDuration)) {
      return _stopsCache[cacheKey] ?? [];
    }

    try {
      final cityConfig = config.ApiConfig.getCityConfig(cityId);
      if (cityConfig == null) {
        debugPrint('Nepodporované město: $cityId');
        return _getMockStops(cityId);
      }

      List<TransportStop> stops = [];

      switch (cityId.toLowerCase()) {
        case 'zagreb':
          stops = await _getZagrebStops();
          break;
        case 'split':
          stops = await _getSplitStops();
          break;
        case 'rijeka':
          stops = await _getRijekaStops();
          break;
        default:
          stops = _getMockStops(cityId);
      }

      // Uložení do cache
      _stopsCache[cacheKey] = stops;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return stops;
    } catch (e) {
      debugPrint('Chyba při načítání zastávek pro $cityId: $e');
      return _getMockStops(cityId);
    }
  }

  /// Získání real-time informací pro zastávku
  Future<List<RealTimeArrival>> getRealTimeArrivals(
    String stopId,
    String cityId,
  ) async {
    final cacheKey = 'realtime_${stopId}_$cityId';

    // Kontrola cache (kratší doba pro real-time data)
    if (_isDataCached(cacheKey, config.ApiConfig.realTimeCacheDuration)) {
      return _realTimeCache[cacheKey] ?? [];
    }

    try {
      final cityConfig = config.ApiConfig.getCityConfig(cityId);
      if (cityConfig == null || !cityConfig.hasRealTime) {
        return _getMockRealTimeArrivals(stopId);
      }

      List<RealTimeArrival> arrivals = [];

      switch (cityId.toLowerCase()) {
        case 'zagreb':
          arrivals = await _getZagrebRealTime(stopId);
          break;
        case 'split':
          arrivals = await _getSplitRealTime(stopId);
          break;
        default:
          arrivals = _getMockRealTimeArrivals(stopId);
      }

      // Uložení do cache
      _realTimeCache[cacheKey] = arrivals;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return arrivals;
    } catch (e) {
      debugPrint('Chyba při načítání real-time dat pro $stopId: $e');
      return _getMockRealTimeArrivals(stopId);
    }
  }

  /// Plánování trasy s kombinací různých API
  Future<List<TransportRoute>> planRoute({
    required double fromLat,
    required double fromLng,
    required double toLat,
    required double toLng,
    DateTime? departureTime,
    List<TransportType> allowedTypes = const [
      TransportType.bus,
      TransportType.tram,
      TransportType.train,
    ],
  }) async {
    try {
      // 1. Detekce města na základě GPS souřadnic
      final fromCity = await _detectCity(fromLat, fromLng);
      final toCity = await _detectCity(toLat, toLng);

      List<TransportRoute> routes = [];

      // 2. Intra-city routing (v rámci jednoho města)
      if (fromCity == toCity && fromCity != null) {
        routes.addAll(
          await _planIntraCityRoute(
            fromLat,
            fromLng,
            toLat,
            toLng,
            fromCity,
            departureTime,
            allowedTypes,
          ),
        );
      }

      // 3. Inter-city routing (mezi městy)
      if (fromCity != toCity) {
        routes.addAll(
          await _planInterCityRoute(
            fromLat,
            fromLng,
            toLat,
            toLng,
            fromCity,
            toCity,
            departureTime,
            allowedTypes,
          ),
        );
      }

      // 4. Seřazení podle kvality
      routes.sort((a, b) => _compareRoutes(a, b));

      return routes.take(5).toList();
    } catch (e) {
      debugPrint('Chyba při plánování trasy: $e');
      return _getMockRoutes(fromLat, fromLng, toLat, toLng, departureTime);
    }
  }

  /// Rezervace jízdenky přes API
  Future<Ticket?> purchaseTicket({
    required String cityId,
    required TicketType type,
    required double price,
    String? routeId,
    String? userId,
  }) async {
    try {
      final cityConfig = config.ApiConfig.getCityConfig(cityId);
      if (cityConfig == null) {
        return _createMockTicket(cityId, type, price);
      }

      final response = await _dio.post(
        '${cityConfig.baseUrl}/tickets/purchase',
        data: {
          'ticket_type': type.name,
          'price': price,
          'currency': 'HRK',
          'route_id': routeId,
          'user_id': userId,
          'purchase_time': DateTime.now().toIso8601String(),
        },
        options: Options(
          headers: config.ApiConfig.getApiHeaders(cityConfig.apiKey),
        ),
      );

      if (response.statusCode == 200) {
        return _parseTicketFromApi(response.data, cityId);
      }
    } catch (e) {
      debugPrint('Chyba při nákupu jízdenky: $e');
    }

    // Fallback na mock ticket
    return _createMockTicket(cityId, type, price);
  }

  /// Získání vlakových spojení
  Future<List<TrainConnection>> getTrainConnections({
    required String fromStation,
    required String toStation,
    DateTime? departureDate,
  }) async {
    // Simulace vlakových spojení
    return [
      TrainConnection(
        id: 'train_001',
        fromStation: fromStation,
        toStation: toStation,
        departureTime:
            departureDate ?? DateTime.now().add(const Duration(hours: 1)),
        arrivalTime:
            (departureDate ?? DateTime.now().add(const Duration(hours: 1))).add(
              const Duration(hours: 2),
            ),
        duration: const Duration(hours: 2),
        price: 89.0,
        currency: 'HRK',
        trainType: 'InterCity',
        stops: [fromStation, 'Karlovac', toStation],
        hasReservation: true,
        carriageClasses: ['2. třída', '1. třída'],
      ),
    ];
  }

  /// Získání trajektových spojení
  Future<List<FerryConnection>> getFerryConnections({
    required String fromPort,
    required String toPort,
    DateTime? departureDate,
  }) async {
    // Simulace trajektových spojení
    return [
      FerryConnection(
        id: 'ferry_001',
        lineNumber: '9602',
        shipName: 'Jadrolinija Split',
        fromPort: fromPort,
        toPort: toPort,
        departureTime:
            departureDate ?? DateTime.now().add(const Duration(hours: 2)),
        arrivalTime:
            (departureDate ?? DateTime.now().add(const Duration(hours: 2))).add(
              const Duration(hours: 2),
            ),
        duration: const Duration(hours: 2),
        price: 45.0,
        currency: 'HRK',
        vehiclePrice: 180.0,
        hasVehicleDeck: true,
        isActive: true,
      ),
    ];
  }

  /// Vyhledání nejbližších zastávek
  Future<List<TransportStop>> findNearbyStops({
    required double latitude,
    required double longitude,
    double radiusKm = 1.0,
  }) async {
    return await _realApi.findNearbyStops(
      latitude: latitude,
      longitude: longitude,
      radiusKm: radiusKm,
    );
  }

  /// Získání dopravních upozornění
  Future<List<TrafficAlert>> getTrafficAlerts({
    String? cityId,
    double? latitude,
    double? longitude,
  }) async {
    return await _realApi.getTrafficAlerts(
      city: cityId,
      latitude: latitude,
      longitude: longitude,
    );
  }

  // ========== PRIVATE METHODS ==========

  /// Kontrola rate limitu
  Future<void> _checkRateLimit() async {
    // Implementace rate limiting logiky
    // Pro demo účely pouze krátká pauza
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Kontrola platnosti cache
  bool _isDataCached(String key, Duration maxAge) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;

    return DateTime.now().difference(timestamp) < maxAge;
  }

  /// Detekce města na základě GPS souřadnic
  Future<String?> _detectCity(double lat, double lng) async {
    // Zagreb bounds
    if (lat >= 45.75 && lat <= 45.85 && lng >= 15.90 && lng <= 16.05) {
      return 'zagreb';
    }
    // Split bounds
    if (lat >= 43.48 && lat <= 43.55 && lng >= 16.40 && lng <= 16.50) {
      return 'split';
    }
    // Rijeka bounds
    if (lat >= 45.32 && lat <= 45.35 && lng >= 14.40 && lng <= 14.45) {
      return 'rijeka';
    }
    // Dubrovnik bounds
    if (lat >= 42.62 && lat <= 42.68 && lng >= 18.05 && lng <= 18.15) {
      return 'dubrovnik';
    }

    return null;
  }

  /// Plánování trasy v rámci města
  Future<List<TransportRoute>> _planIntraCityRoute(
    double fromLat,
    double fromLng,
    double toLat,
    double toLng,
    String cityId,
    DateTime? departureTime,
    List<TransportType> allowedTypes,
  ) async {
    // Implementace specifická pro každé město
    switch (cityId) {
      case 'zagreb':
        // Konverze PublicTransport na TransportRoute
        final publicTransport = await _realApi.getZagrebPublicTransport();
        return _convertPublicTransportToRoutes(publicTransport);
      case 'split':
        // Konverze PublicTransport na TransportRoute
        final publicTransport = await _realApi.getSplitPublicTransport();
        return _convertPublicTransportToRoutes(publicTransport);
      default:
        return [];
    }
  }

  /// Plánování trasy mezi městy
  Future<List<TransportRoute>> _planInterCityRoute(
    double fromLat,
    double fromLng,
    double toLat,
    double toLng,
    String? fromCity,
    String? toCity,
    DateTime? departureTime,
    List<TransportType> allowedTypes,
  ) async {
    final routes = <TransportRoute>[];

    // Vlakové spojení
    if (allowedTypes.contains(TransportType.train)) {
      try {
        final trainConnections = await getTrainConnections(
          fromStation: fromCity ?? 'Unknown',
          toStation: toCity ?? 'Unknown',
          departureDate: departureTime,
        );
        // TODO: Implementovat konverzi TrainConnection na TransportRoute
        debugPrint('Nalezeno ${trainConnections.length} vlakových spojení');
      } catch (e) {
        debugPrint('Chyba při načítání vlakových spojení: $e');
      }
    }

    // Trajektové spojení
    if (allowedTypes.contains(TransportType.ferry)) {
      try {
        final ferryConnections = await getFerryConnections(
          fromPort: fromCity ?? 'Unknown',
          toPort: toCity ?? 'Unknown',
          departureDate: departureTime,
        );
        // TODO: Implementovat konverzi FerryConnection na TransportRoute
        debugPrint('Nalezeno ${ferryConnections.length} trajektových spojení');
      } catch (e) {
        debugPrint('Chyba při načítání trajektových spojení: $e');
      }
    }

    return routes;
  }

  /// Porovnání kvality tras
  int _compareRoutes(TransportRoute a, TransportRoute b) {
    // Priorita: 1. doba trvání, 2. počet přestupů, 3. cena
    final durationDiff = a.totalDuration.compareTo(b.totalDuration);
    if (durationDiff != 0) return durationDiff;

    final transfersDiff = a.transfers.compareTo(b.transfers);
    if (transfersDiff != 0) return transfersDiff;

    return a.totalPrice.compareTo(b.totalPrice);
  }

  // ========== CITY-SPECIFIC API METHODS ==========

  Future<List<TransportStop>> _getZagrebStops() async {
    // Implementace pro ZET Zagreb API
    return _getMockStops('zagreb');
  }

  Future<List<TransportStop>> _getSplitStops() async {
    // Implementace pro Promet Split API
    return _getMockStops('split');
  }

  Future<List<TransportStop>> _getRijekaStops() async {
    // Implementace pro Autotrolej Rijeka API
    return _getMockStops('rijeka');
  }

  Future<List<RealTimeArrival>> _getZagrebRealTime(String stopId) async {
    // Implementace pro ZET real-time API
    return _getMockRealTimeArrivals(stopId);
  }

  Future<List<RealTimeArrival>> _getSplitRealTime(String stopId) async {
    // Implementace pro Split real-time API
    return _getMockRealTimeArrivals(stopId);
  }

  // ========== MOCK DATA METHODS ==========

  List<TransportStop> _getMockStops(String cityId) {
    return [
      TransportStop(
        id: '${cityId}_stop_001',
        name: 'Hlavní nádraží',
        latitude: 45.8050,
        longitude: 15.9819,
        city: cityId,
        zone: '1',
        platforms: ['A', 'B'],
        facilities: ['shelter', 'bench', 'display'],
        isAccessible: true,
        hasRealTimeInfo: true,
        arrivals: _getMockRealTimeArrivals('${cityId}_stop_001'),
      ),
      TransportStop(
        id: '${cityId}_stop_002',
        name: 'Centrum',
        latitude: 45.8131,
        longitude: 15.9775,
        city: cityId,
        zone: '1',
        platforms: ['1', '2'],
        facilities: ['shelter', 'display'],
        isAccessible: true,
        hasRealTimeInfo: true,
        arrivals: _getMockRealTimeArrivals('${cityId}_stop_002'),
      ),
    ];
  }

  List<RealTimeArrival> _getMockRealTimeArrivals(String stopId) {
    final now = DateTime.now();
    return [
      RealTimeArrival(
        routeNumber: '6',
        direction: 'Črnomerec',
        scheduledTime: now.add(const Duration(minutes: 3)),
        estimatedTime: now.add(const Duration(minutes: 4)),
        delay: const Duration(minutes: 1),
        vehicleId: 'TMK2001',
        isRealTime: true,
        message: 'Mírné zpoždění',
      ),
      RealTimeArrival(
        routeNumber: '14',
        direction: 'Mihaljevac',
        scheduledTime: now.add(const Duration(minutes: 8)),
        estimatedTime: now.add(const Duration(minutes: 8)),
        delay: Duration.zero,
        vehicleId: 'BUS301',
        isRealTime: true,
      ),
    ];
  }

  List<TransportRoute> _getMockRoutes(
    double fromLat,
    double fromLng,
    double toLat,
    double toLng,
    DateTime? departureTime,
  ) {
    final departure =
        departureTime ?? DateTime.now().add(const Duration(minutes: 5));

    return [
      TransportRoute(
        id: 'route_mock_001',
        fromStopId: 'stop_from',
        toStopId: 'stop_to',
        segments: [
          RouteSegment(
            type: SegmentType.walking,
            duration: const Duration(minutes: 5),
            distance: 400,
            instructions: 'Dojděte na zastávku',
          ),
          RouteSegment(
            type: SegmentType.bus,
            routeNumber: '14',
            fromStopName: 'Zastávka A',
            toStopName: 'Zastávka B',
            duration: const Duration(minutes: 17),
            distance: 8500,
            price: 4.0,
            instructions: 'Autobus 14 směr Centrum',
          ),
        ],
        totalDuration: const Duration(minutes: 22),
        totalDistance: 8900,
        totalPrice: 4.0,
        currency: 'HRK',
        departureTime: departure,
        arrivalTime: departure.add(const Duration(minutes: 22)),
        transfers: 0,
        routeType: RouteType.fastest,
      ),
    ];
  }

  Ticket _createMockTicket(String cityId, TicketType type, double price) {
    final now = DateTime.now();
    return Ticket(
      id: 'ticket_${now.millisecondsSinceEpoch}',
      type: type,
      city: cityId,
      price: price,
      currency: 'HRK',
      purchaseTime: now,
      validFrom: now,
      validUntil: _getTicketValidUntil(type, now),
      status: TicketStatus.active,
      qrCode: 'QR_${now.millisecondsSinceEpoch}',
    );
  }

  DateTime _getTicketValidUntil(TicketType type, DateTime from) {
    switch (type) {
      case TicketType.single:
        return from.add(const Duration(hours: 1));
      case TicketType.daily:
        return from.add(const Duration(days: 1));
      case TicketType.weekly:
        return from.add(const Duration(days: 7));
      case TicketType.monthly:
        return from.add(const Duration(days: 30));
      default:
        return from.add(const Duration(hours: 1));
    }
  }

  Ticket _parseTicketFromApi(Map<String, dynamic> data, String cityId) {
    return Ticket(
      id: data['id']?.toString() ?? '',
      type: TicketType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => TicketType.single,
      ),
      city: cityId,
      price: data['price']?.toDouble() ?? 0.0,
      currency: data['currency']?.toString() ?? 'HRK',
      purchaseTime: DateTime.parse(data['purchase_time']),
      validFrom: DateTime.parse(data['valid_from']),
      validUntil: DateTime.parse(data['valid_until']),
      status: TicketStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => TicketStatus.active,
      ),
      qrCode: data['qr_code']?.toString() ?? '',
    );
  }

  /// Konverze PublicTransport na TransportRoute
  List<TransportRoute> _convertPublicTransportToRoutes(
    List<PublicTransport> publicTransport,
  ) {
    return publicTransport.map((pt) {
      return TransportRoute(
        id: pt.id,
        fromStopId: 'unknown',
        toStopId: 'unknown',
        segments: [
          RouteSegment(
            type: SegmentType.bus,
            routeNumber: pt.routeNumber,
            fromStopName: 'Start',
            toStopName: 'End',
            duration: const Duration(minutes: 30),
            distance: 5000,
            price: 4.0,
            instructions: 'Linka ${pt.routeNumber}',
          ),
        ],
        totalDuration: const Duration(minutes: 30),
        totalDistance: 5000,
        totalPrice: 4.0,
        currency: 'HRK',
        departureTime: DateTime.now(),
        arrivalTime: DateTime.now().add(const Duration(minutes: 30)),
        transfers: 0,
        routeType: RouteType.fastest,
      );
    }).toList();
  }

  /// Vyčištění cache
  void clearCache() {
    _stopsCache.clear();
    _realTimeCache.clear();
    _cacheTimestamps.clear();
  }

  /// Dispose
  void dispose() {
    clearCache();
  }
}

// ========== HELPER CLASSES ==========

class TrainConnection {
  final String id;
  final String fromStation;
  final String toStation;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final Duration duration;
  final double price;
  final String currency;
  final String trainType;
  final List<String> stops;
  final bool hasReservation;
  final List<String> carriageClasses;

  TrainConnection({
    required this.id,
    required this.fromStation,
    required this.toStation,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.price,
    required this.currency,
    required this.trainType,
    required this.stops,
    required this.hasReservation,
    required this.carriageClasses,
  });
}

class FerryConnection {
  final String id;
  final String lineNumber;
  final String shipName;
  final String fromPort;
  final String toPort;
  final DateTime departureTime;
  final DateTime arrivalTime;
  final Duration duration;
  final double price;
  final String currency;
  final double vehiclePrice;
  final bool hasVehicleDeck;
  final bool isActive;

  FerryConnection({
    required this.id,
    required this.lineNumber,
    required this.shipName,
    required this.fromPort,
    required this.toPort,
    required this.departureTime,
    required this.arrivalTime,
    required this.duration,
    required this.price,
    required this.currency,
    required this.vehiclePrice,
    required this.hasVehicleDeck,
    required this.isActive,
  });
}
