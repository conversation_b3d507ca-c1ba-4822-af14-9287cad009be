import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/diary_entry.dart';
import '../services/export_service.dart';

class DiaryScreen extends StatefulWidget {
  const DiaryScreen({super.key});

  @override
  State<DiaryScreen> createState() => _DiaryScreenState();
}

class _DiaryScreenState extends State<DiaryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ExportService _exportService = ExportService();

  List<DiaryEntry> _entries = [];
  List<DiaryEntry> _filteredEntries = [];
  bool _isLoading = true;
  String _searchQuery = '';
  DiaryMood? _selectedMood;
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDiaryEntries();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDiaryEntries() async {
    setState(() => _isLoading = true);

    // Simulace načtení dat - v reálné aplikaci by se načítalo z databáze
    await Future.delayed(const Duration(milliseconds: 500));

    final mockEntries = [
      DiaryEntry(
        id: '1',
        title: 'První den v Dubrovniku',
        content:
            'Dnes jsem dorazil do nádherného Dubrovniku. Město je úžasné, staré hradby jsou impozantní. Prošel jsem si celé historické centrum a vyfotil spoustu krásných míst.',
        date: DateTime.now().subtract(const Duration(days: 2)),
        location: 'Dubrovnik, Chorvatsko',
        latitude: 42.6507,
        longitude: 18.0944,
        photos: ['photo1.jpg', 'photo2.jpg'],
        voiceNotes: ['voice1'],
        tags: ['dubrovnik', 'hradby', 'historie'],
        mood: DiaryMood.excited,
        weather: 'Slunečno, 28°C',
        rating: 5.0,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      DiaryEntry(
        id: '2',
        title: 'Výlet na ostrov Lokrum',
        content:
            'Krásný den strávený na ostrově Lokrum. Navštívil jsem botanickou zahradu a vykoupal se v krásném moři. Večer jsme měli skvělou večeři v místní restauraci.',
        date: DateTime.now().subtract(const Duration(days: 1)),
        location: 'Lokrum, Chorvatsko',
        latitude: 42.6167,
        longitude: 18.1167,
        photos: ['photo3.jpg'],
        tags: ['lokrum', 'ostrov', 'moře'],
        mood: DiaryMood.relaxed,
        weather: 'Částečně oblačno, 26°C',
        rating: 4.5,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    setState(() {
      _entries = mockEntries;
      _filteredEntries = mockEntries;
      _isLoading = false;
    });
  }

  void _filterEntries() {
    setState(() {
      _filteredEntries = _entries.where((entry) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            entry.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            entry.content.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            entry.tags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
            );

        final matchesMood =
            _selectedMood == null || entry.mood == _selectedMood;

        final matchesDate =
            _selectedDate == null ||
            (entry.date.year == _selectedDate!.year &&
                entry.date.month == _selectedDate!.month &&
                entry.date.day == _selectedDate!.day);

        return matchesSearch && matchesMood && matchesDate;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cestovatelský deník'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
          IconButton(
            onPressed: _showExportDialog,
            icon: const Icon(Icons.share),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.list), text: 'Záznamy'),
            Tab(icon: Icon(Icons.calendar_month), text: 'Kalendář'),
            Tab(icon: Icon(Icons.analytics), text: 'Statistiky'),
            Tab(icon: Icon(Icons.description), text: 'Šablony'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildEntriesList(),
          _buildCalendarView(),
          _buildStatisticsView(),
          _buildTemplatesView(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewEntry,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEntriesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné záznamy',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Začněte psát svůj cestovatelský deník',
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewEntry,
              icon: const Icon(Icons.add),
              label: const Text('Vytvořit první záznam'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Vyhledávací pole
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Hledat v deníku...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterEntries();
            },
          ),
        ),

        // Seznam záznamů
        Expanded(
          child: ListView.builder(
            itemCount: _filteredEntries.length,
            itemBuilder: (context, index) {
              final entry = _filteredEntries[index];
              return _buildEntryCard(entry);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEntryCard(DiaryEntry entry) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _openEntryDetail(entry),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hlavička s datem a náladou
              Row(
                children: [
                  Text(
                    DateFormat('dd.MM.yyyy').format(entry.date),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  if (entry.mood != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      entry.mood!.emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                  const Spacer(),
                  if (entry.rating != null)
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.amber),
                        Text(
                          entry.rating!.toStringAsFixed(1),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Titulek
              Text(
                entry.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 8),

              // Lokace
              if (entry.location != null)
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        entry.location!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 8),

              // Excerpt obsahu
              Text(
                entry.excerpt,
                style: const TextStyle(fontSize: 14),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Indikátory médií a tagy
              Row(
                children: [
                  if (entry.hasPhotos)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.photo, size: 14, color: Colors.blue),
                          const SizedBox(width: 4),
                          Text(
                            '${entry.photos.length}',
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),

                  if (entry.hasVoiceNotes) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.mic, size: 14, color: Colors.green),
                          const SizedBox(width: 4),
                          Text(
                            '${entry.voiceNotes.length}',
                            style: const TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  const Spacer(),

                  // Počet slov
                  Text(
                    '${entry.wordCount} slov',
                    style: TextStyle(color: Colors.grey[500], fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.calendar_month, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Kalendářové zobrazení',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Zobrazení záznamů v kalendáři',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsView() {
    final totalEntries = _entries.length;
    final totalWords = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.wordCount,
    );
    final totalPhotos = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.photos.length,
    );
    final totalVoiceNotes = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.voiceNotes.length,
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Statistiky deníku',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Základní statistiky
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Záznamy',
                  totalEntries.toString(),
                  Icons.book,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Slova',
                  totalWords.toString(),
                  Icons.text_fields,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Fotografie',
                  totalPhotos.toString(),
                  Icons.photo,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Hlasové poznámky',
                  totalVoiceNotes.toString(),
                  Icons.mic,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Nálady
          const Text(
            'Nálady',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          ..._buildMoodStatistics(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMoodStatistics() {
    final moodCounts = <DiaryMood, int>{};
    for (final entry in _entries) {
      if (entry.mood != null) {
        moodCounts[entry.mood!] = (moodCounts[entry.mood!] ?? 0) + 1;
      }
    }

    return moodCounts.entries.map((entry) {
      final percentage = _entries.isNotEmpty
          ? (entry.value / _entries.length * 100).round()
          : 0;

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Text(entry.key.emoji, style: const TextStyle(fontSize: 20)),
            const SizedBox(width: 8),
            Expanded(child: Text(entry.key.displayName)),
            Text('${entry.value}x ($percentage%)'),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildTemplatesView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Šablony pro deník',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: DiaryTemplates.templates.length,
              itemBuilder: (context, index) {
                final template = DiaryTemplates.templates[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: ListTile(
                    leading: const Icon(Icons.description),
                    title: Text(template['title'] as String),
                    subtitle: Text(
                      (template['content'] as String).split('\n').first,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _useTemplate(template),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _createNewEntry() {
    // Navigace na obrazovku pro vytvoření nového záznamu
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => const DiaryEntryEditScreen()),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries(); // Obnovit seznam po vytvoření
          }
        });
  }

  void _openEntryDetail(DiaryEntry entry) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DiaryEntryDetailScreen(entry: entry),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries(); // Obnovit seznam po úpravě
          }
        });
  }

  void _useTemplate(Map<String, dynamic> template) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DiaryEntryEditScreen(template: template),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries();
          }
        });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrovat záznamy'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Filtr podle nálady
            DropdownButtonFormField<DiaryMood?>(
              value: _selectedMood,
              decoration: const InputDecoration(labelText: 'Nálada'),
              items: [
                const DropdownMenuItem<DiaryMood?>(
                  value: null,
                  child: Text('Všechny nálady'),
                ),
                ...DiaryMood.values.map(
                  (mood) => DropdownMenuItem(
                    value: mood,
                    child: Text('${mood.emoji} ${mood.displayName}'),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedMood = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Filtr podle data
            ListTile(
              title: Text(
                _selectedDate == null
                    ? 'Vybrat datum'
                    : DateFormat('dd.MM.yyyy').format(_selectedDate!),
              ),
              leading: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _selectedDate = date;
                  });
                }
              },
            ),

            if (_selectedDate != null)
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedDate = null;
                  });
                },
                child: const Text('Zrušit filtr data'),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedMood = null;
                _selectedDate = null;
              });
              _filterEntries();
              Navigator.of(context).pop();
            },
            child: const Text('Zrušit filtry'),
          ),
          ElevatedButton(
            onPressed: () {
              _filterEntries();
              Navigator.of(context).pop();
            },
            child: const Text('Použít'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export deníku'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.picture_as_pdf),
              title: Text('Export do PDF'),
              subtitle: Text('Vytvoří PDF soubor s vašimi záznamy'),
            ),
            ListTile(
              leading: Icon(Icons.code),
              title: Text('Export do HTML'),
              subtitle: Text('Vytvoří webovou stránku s deníkem'),
            ),
            ListTile(
              leading: Icon(Icons.share),
              title: Text('Sdílet záznamy'),
              subtitle: Text('Sdílet vybrané záznamy na sociálních sítích'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportToPdf();
            },
            child: const Text('Export PDF'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToPdf() async {
    try {
      final filePath = await _exportService.exportDiaryToPdf(
        entries: _entries,
        title: 'Můj cestovatelský deník',
        includePhotos: true,
        includeVoiceNotes: false,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deník exportován: $filePath'),
            action: SnackBarAction(
              label: 'Otevřít',
              onPressed: () {
                // Otevřít soubor
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při exportu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// Placeholder obrazovky pro navigaci
class DiaryEntryEditScreen extends StatelessWidget {
  final Map<String, dynamic>? template;

  const DiaryEntryEditScreen({super.key, this.template});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Nový záznam')),
      body: const Center(child: Text('Obrazovka pro editaci záznamu')),
    );
  }
}

class DiaryEntryDetailScreen extends StatelessWidget {
  final DiaryEntry entry;

  const DiaryEntryDetailScreen({super.key, required this.entry});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(entry.title)),
      body: const Center(child: Text('Detail záznamu')),
    );
  }
}
