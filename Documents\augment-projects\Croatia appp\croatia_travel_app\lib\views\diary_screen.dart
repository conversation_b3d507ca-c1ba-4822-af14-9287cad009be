import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../models/diary_entry.dart';
import '../services/export_service.dart';

class DiaryScreen extends StatefulWidget {
  const DiaryScreen({super.key});

  @override
  State<DiaryScreen> createState() => _DiaryScreenState();
}

class _DiaryScreenState extends State<DiaryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ExportService _exportService = ExportService();

  List<DiaryEntry> _entries = [];
  List<DiaryEntry> _filteredEntries = [];
  bool _isLoading = true;
  String _searchQuery = '';
  DiaryMood? _selectedMood;
  DateTime? _selectedDate;

  // Kalendář proměnné
  DateTime _currentMonth = DateTime.now();
  DateTime? _selectedCalendarDate;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDiaryEntries();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadDiaryEntries() async {
    setState(() => _isLoading = true);

    // Simulace načtení dat - v reálné aplikaci by se načítalo z databáze
    await Future.delayed(const Duration(milliseconds: 500));

    final mockEntries = [
      DiaryEntry(
        id: '1',
        title: 'První den v Dubrovniku',
        content:
            'Dnes jsem dorazil do nádherného Dubrovniku. Město je úžasné, staré hradby jsou impozantní. Prošel jsem si celé historické centrum a vyfotil spoustu krásných míst.',
        date: DateTime.now().subtract(const Duration(days: 2)),
        location: 'Dubrovnik, Chorvatsko',
        latitude: 42.6507,
        longitude: 18.0944,
        photos: ['photo1.jpg', 'photo2.jpg'],
        voiceNotes: ['voice1'],
        tags: ['dubrovnik', 'hradby', 'historie'],
        mood: DiaryMood.excited,
        weather: 'Slunečno, 28°C',
        rating: 5.0,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      DiaryEntry(
        id: '2',
        title: 'Výlet na ostrov Lokrum',
        content:
            'Krásný den strávený na ostrově Lokrum. Navštívil jsem botanickou zahradu a vykoupal se v krásném moři. Večer jsme měli skvělou večeři v místní restauraci.',
        date: DateTime.now().subtract(const Duration(days: 1)),
        location: 'Lokrum, Chorvatsko',
        latitude: 42.6167,
        longitude: 18.1167,
        photos: ['photo3.jpg'],
        tags: ['lokrum', 'ostrov', 'moře'],
        mood: DiaryMood.relaxed,
        weather: 'Částečně oblačno, 26°C',
        rating: 4.5,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];

    setState(() {
      _entries = mockEntries;
      _filteredEntries = mockEntries;
      _isLoading = false;
    });
  }

  void _filterEntries() {
    setState(() {
      _filteredEntries = _entries.where((entry) {
        final matchesSearch =
            _searchQuery.isEmpty ||
            entry.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            entry.content.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            entry.tags.any(
              (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
            );

        final matchesMood =
            _selectedMood == null || entry.mood == _selectedMood;

        final matchesDate =
            _selectedDate == null ||
            (entry.date.year == _selectedDate!.year &&
                entry.date.month == _selectedDate!.month &&
                entry.date.day == _selectedDate!.day);

        return matchesSearch && matchesMood && matchesDate;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cestovatelský deník'),
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
          ),
          IconButton(
            onPressed: _showExportDialog,
            icon: const Icon(Icons.share),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.auto_awesome), text: 'Hlavní'),
            Tab(icon: Icon(Icons.list), text: 'Záznamy'),
            Tab(icon: Icon(Icons.calendar_month), text: 'Kalendář'),
            Tab(icon: Icon(Icons.analytics), text: 'Statistiky'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMainDiaryView(), // Nová krásná hlavní obrazovka
          _buildEntriesListView(), // Přejmenovaný starý seznam
          _buildCalendarView(),
          _buildStatisticsView(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewEntry,
        child: const Icon(Icons.add),
      ),
    );
  }

  // Nová krásná hlavní obrazovka podle obrázku
  Widget _buildMainDiaryView() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFFF8F6F0), // Krémový papír
            Color(0xFFE8E6E0), // Světlejší krémová
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Stack(
        children: [
          // Watercolor pozadí Chorvatska
          Positioned(
            top: 100,
            left: -100,
            right: -100,
            bottom: 200,
            child: _buildWatercolorBackground(),
          ),

          // Hlavní obsah
          Column(
            children: [
              // Header podle obrázku
              Container(
                padding: const EdgeInsets.fromLTRB(24, 60, 24, 40),
                child: Column(
                  children: [
                    // Hlavní nadpis
                    Text(
                      'Adriatic Diary',
                      style: GoogleFonts.playfairDisplay(
                        fontSize: 42,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF006994), // Jaderská modrá
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 12),

                    // Podnadpis
                    Text(
                      'Luxusní a elegantní rozhraní',
                      style: GoogleFonts.inter(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFFFF6B35), // Oranžová západ slunce
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Telefon mockup podle obrázku
              Expanded(
                child: Center(
                  child: Container(
                    width: 280,
                    height: 500,
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(35),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 30,
                          offset: const Offset(0, 15),
                        ),
                      ],
                    ),
                    child: Container(
                      margin: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(31),
                      ),
                      child: _buildPhoneContent(),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 40),
            ],
          ),
        ],
      ),
    );
  }

  // Starý seznam záznamů (nyní druhý tab)
  Widget _buildEntriesListView() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredEntries.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.book_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Žádné záznamy',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Začněte psát svůj cestovatelský deník',
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewEntry,
              icon: const Icon(Icons.add),
              label: const Text('Vytvořit první záznam'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Vyhledávací pole
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'Hledat v deníku...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterEntries();
            },
          ),
        ),

        // Seznam záznamů
        Expanded(
          child: ListView.builder(
            itemCount: _filteredEntries.length,
            itemBuilder: (context, index) {
              final entry = _filteredEntries[index];
              return _buildEntryCard(entry);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEntryCard(DiaryEntry entry) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => _openEntryDetail(entry),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hlavička s datem a náladou
              Row(
                children: [
                  Text(
                    DateFormat('dd.MM.yyyy').format(entry.date),
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                  if (entry.mood != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      entry.mood!.emoji,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                  const Spacer(),
                  if (entry.rating != null)
                    Row(
                      children: [
                        const Icon(Icons.star, size: 16, color: Colors.amber),
                        Text(
                          entry.rating!.toStringAsFixed(1),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Titulek
              Text(
                entry.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 8),

              // Lokace
              if (entry.location != null)
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        entry.location!,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 8),

              // Excerpt obsahu
              Text(
                entry.excerpt,
                style: const TextStyle(fontSize: 14),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Indikátory médií a tagy
              Row(
                children: [
                  if (entry.hasPhotos)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.photo, size: 14, color: Colors.blue),
                          const SizedBox(width: 4),
                          Text(
                            '${entry.photos.length}',
                            style: const TextStyle(
                              color: Colors.blue,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),

                  if (entry.hasVoiceNotes) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.mic, size: 14, color: Colors.green),
                          const SizedBox(width: 4),
                          Text(
                            '${entry.voiceNotes.length}',
                            style: const TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  const Spacer(),

                  // Počet slov
                  Text(
                    '${entry.wordCount} slov',
                    style: TextStyle(color: Colors.grey[500], fontSize: 12),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarView() {
    return Container(
      color: const Color(0xFFF8F6F0), // Krémový papír pozadí
      child: Stack(
        children: [
          // Watercolor mapa Chorvatska v pozadí
          Positioned(right: -50, top: 50, child: _buildWatercolorCroatiaMap()),

          Center(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Telefon mockup podle obrázku
                  Container(
                    width: 300,
                    height: 500,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Header kalendáře
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              IconButton(
                                onPressed: () => _changeMonth(-1),
                                icon: const Icon(
                                  Icons.chevron_left,
                                  color: Color(0xFF006994),
                                ),
                              ),
                              Text(
                                DateFormat('MMMM yyyy').format(_currentMonth),
                                style: GoogleFonts.inter(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF006994),
                                ),
                              ),
                              IconButton(
                                onPressed: () => _changeMonth(1),
                                icon: const Icon(
                                  Icons.chevron_right,
                                  color: Color(0xFF006994),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Dny v týdnu
                          Row(
                            children: ['M', 'T', 'W', 'T', 'F', 'S', 'S']
                                .map(
                                  (day) => Expanded(
                                    child: Center(
                                      child: Text(
                                        day,
                                        style: GoogleFonts.inter(
                                          color: const Color(0xFF666666),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),

                          const SizedBox(height: 12),

                          // Kalendářová mřížka podle obrázku
                          Expanded(child: _buildPhoneMockupCalendar()),

                          const SizedBox(height: 20),

                          // Text podle obrázku
                          Text(
                            'Interactive travel calendar',
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              color: const Color(0xFF666666),
                            ),
                          ),

                          const SizedBox(height: 8),

                          Text(
                            'Map with geotagged memories',
                            style: GoogleFonts.inter(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF2E8B8B),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsView() {
    final totalEntries = _entries.length;
    final totalWords = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.wordCount,
    );
    final totalPhotos = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.photos.length,
    );
    final totalVoiceNotes = _entries.fold<int>(
      0,
      (sum, entry) => sum + entry.voiceNotes.length,
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Statistiky deníku',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Základní statistiky
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Záznamy',
                  totalEntries.toString(),
                  Icons.book,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Slova',
                  totalWords.toString(),
                  Icons.text_fields,
                  Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Fotografie',
                  totalPhotos.toString(),
                  Icons.photo,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'Hlasové poznámky',
                  totalVoiceNotes.toString(),
                  Icons.mic,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Nálady
          const Text(
            'Nálady',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          ..._buildMoodStatistics(),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: TextStyle(color: Colors.grey[600], fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMoodStatistics() {
    final moodCounts = <DiaryMood, int>{};
    for (final entry in _entries) {
      if (entry.mood != null) {
        moodCounts[entry.mood!] = (moodCounts[entry.mood!] ?? 0) + 1;
      }
    }

    return moodCounts.entries.map((entry) {
      final percentage = _entries.isNotEmpty
          ? (entry.value / _entries.length * 100).round()
          : 0;

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Text(entry.key.emoji, style: const TextStyle(fontSize: 20)),
            const SizedBox(width: 8),
            Expanded(child: Text(entry.key.displayName)),
            Text('${entry.value}x ($percentage%)'),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildTemplatesView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Šablony pro deník',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: DiaryTemplates.templates.length,
              itemBuilder: (context, index) {
                final template = DiaryTemplates.templates[index];
                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: ListTile(
                    leading: const Icon(Icons.description),
                    title: Text(template['title'] as String),
                    subtitle: Text(
                      (template['content'] as String).split('\n').first,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _useTemplate(template),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _createNewEntry() {
    // Navigace na obrazovku pro vytvoření nového záznamu
    Navigator.of(context)
        .push(
          MaterialPageRoute(builder: (context) => const DiaryEntryEditScreen()),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries(); // Obnovit seznam po vytvoření
          }
        });
  }

  void _openEntryDetail(DiaryEntry entry) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DiaryEntryDetailScreen(entry: entry),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries(); // Obnovit seznam po úpravě
          }
        });
  }

  void _useTemplate(Map<String, dynamic> template) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => DiaryEntryEditScreen(template: template),
          ),
        )
        .then((result) {
          if (result == true) {
            _loadDiaryEntries();
          }
        });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrovat záznamy'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Filtr podle nálady
            DropdownButtonFormField<DiaryMood?>(
              value: _selectedMood,
              decoration: const InputDecoration(labelText: 'Nálada'),
              items: [
                const DropdownMenuItem<DiaryMood?>(
                  value: null,
                  child: Text('Všechny nálady'),
                ),
                ...DiaryMood.values.map(
                  (mood) => DropdownMenuItem(
                    value: mood,
                    child: Text('${mood.emoji} ${mood.displayName}'),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedMood = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // Filtr podle data
            ListTile(
              title: Text(
                _selectedDate == null
                    ? 'Vybrat datum'
                    : DateFormat('dd.MM.yyyy').format(_selectedDate!),
              ),
              leading: const Icon(Icons.calendar_today),
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate ?? DateTime.now(),
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                );
                if (date != null) {
                  setState(() {
                    _selectedDate = date;
                  });
                }
              },
            ),

            if (_selectedDate != null)
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedDate = null;
                  });
                },
                child: const Text('Zrušit filtr data'),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedMood = null;
                _selectedDate = null;
              });
              _filterEntries();
              Navigator.of(context).pop();
            },
            child: const Text('Zrušit filtry'),
          ),
          ElevatedButton(
            onPressed: () {
              _filterEntries();
              Navigator.of(context).pop();
            },
            child: const Text('Použít'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export deníku'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.picture_as_pdf),
              title: Text('Export do PDF'),
              subtitle: Text('Vytvoří PDF soubor s vašimi záznamy'),
            ),
            ListTile(
              leading: Icon(Icons.code),
              title: Text('Export do HTML'),
              subtitle: Text('Vytvoří webovou stránku s deníkem'),
            ),
            ListTile(
              leading: Icon(Icons.share),
              title: Text('Sdílet záznamy'),
              subtitle: Text('Sdílet vybrané záznamy na sociálních sítích'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Zrušit'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportToPdf();
            },
            child: const Text('Export PDF'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToPdf() async {
    try {
      final filePath = await _exportService.exportDiaryToPdf(
        entries: _entries,
        title: 'Můj cestovatelský deník',
        includePhotos: true,
        includeVoiceNotes: false,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deník exportován: $filePath'),
            action: SnackBarAction(
              label: 'Otevřít',
              onPressed: () {
                // Otevřít soubor
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Chyba při exportu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Kalendářové metody
  void _changeMonth(int direction) {
    setState(() {
      _currentMonth = DateTime(
        _currentMonth.year,
        _currentMonth.month + direction,
        1,
      );
    });
  }

  Widget _buildPhoneMockupCalendar() {
    final firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    final lastDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    );
    final firstDayWeekday = firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;

    // Předchozí měsíc pro vyplnění
    final previousMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month - 1,
      0,
    );
    final daysFromPreviousMonth = firstDayWeekday - 1;

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: 42, // 6 týdnů × 7 dní
      itemBuilder: (context, index) {
        DateTime cellDate;
        bool isCurrentMonth = true;
        bool isToday = false;

        if (index < daysFromPreviousMonth) {
          // Dny z předchozího měsíce
          cellDate = DateTime(
            previousMonth.year,
            previousMonth.month,
            previousMonth.day - (daysFromPreviousMonth - index - 1),
          );
          isCurrentMonth = false;
        } else if (index < daysFromPreviousMonth + daysInMonth) {
          // Dny aktuálního měsíce
          cellDate = DateTime(
            _currentMonth.year,
            _currentMonth.month,
            index - daysFromPreviousMonth + 1,
          );
          final today = DateTime.now();
          isToday =
              cellDate.year == today.year &&
              cellDate.month == today.month &&
              cellDate.day == today.day;
        } else {
          // Dny z následujícího měsíce
          cellDate = DateTime(
            _currentMonth.year,
            _currentMonth.month + 1,
            index - daysFromPreviousMonth - daysInMonth + 1,
          );
          isCurrentMonth = false;
        }

        // Zkontroluj, jestli má tento den záznam (simulace podle obrázku)
        final hasMemory =
            isCurrentMonth && [5, 12, 18, 25].contains(cellDate.day);

        return _buildPhoneMockupDay(
          cellDate,
          isCurrentMonth,
          isToday,
          hasMemory,
        );
      },
    );
  }

  Widget _buildPhoneMockupDay(
    DateTime date,
    bool isCurrentMonth,
    bool isToday,
    bool hasMemory,
  ) {
    Color backgroundColor = Colors.transparent;
    Color textColor = isCurrentMonth
        ? const Color(0xFF2C2C2C)
        : const Color(0xFF2C2C2C).withValues(alpha: 0.3);

    if (hasMemory) {
      backgroundColor = const Color(
        0xFFFF6B35,
      ).withValues(alpha: 0.1); // Jemné oranžové pozadí
    }

    return GestureDetector(
      onTap: isCurrentMonth ? () => _showDayEntries(date) : null,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: hasMemory && isCurrentMonth
              ? const Text(
                  '😊', // Emoji podle obrázku
                  style: TextStyle(fontSize: 16),
                )
              : Text(
                  date.day.toString(),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: textColor,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month,
      1,
    );
    final lastDayOfMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month + 1,
      0,
    );
    final firstDayWeekday = firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;

    // Předchozí měsíc pro vyplnění
    final previousMonth = DateTime(
      _currentMonth.year,
      _currentMonth.month - 1,
      0,
    );
    final daysFromPreviousMonth = firstDayWeekday - 1;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 7,
          childAspectRatio: 1,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: 42, // 6 týdnů × 7 dní
        itemBuilder: (context, index) {
          DateTime cellDate;
          bool isCurrentMonth = true;
          bool isToday = false;

          if (index < daysFromPreviousMonth) {
            // Dny z předchozího měsíce
            cellDate = DateTime(
              previousMonth.year,
              previousMonth.month,
              previousMonth.day - (daysFromPreviousMonth - index - 1),
            );
            isCurrentMonth = false;
          } else if (index < daysFromPreviousMonth + daysInMonth) {
            // Dny aktuálního měsíce
            cellDate = DateTime(
              _currentMonth.year,
              _currentMonth.month,
              index - daysFromPreviousMonth + 1,
            );
            final today = DateTime.now();
            isToday =
                cellDate.year == today.year &&
                cellDate.month == today.month &&
                cellDate.day == today.day;
          } else {
            // Dny z následujícího měsíce
            cellDate = DateTime(
              _currentMonth.year,
              _currentMonth.month + 1,
              index - daysFromPreviousMonth - daysInMonth + 1,
            );
            isCurrentMonth = false;
          }

          // Zkontroluj, jestli má tento den záznam
          final hasEntry = _entries.any(
            (entry) =>
                entry.date.year == cellDate.year &&
                entry.date.month == cellDate.month &&
                entry.date.day == cellDate.day,
          );

          final isSelected =
              _selectedCalendarDate != null &&
              _selectedCalendarDate!.year == cellDate.year &&
              _selectedCalendarDate!.month == cellDate.month &&
              _selectedCalendarDate!.day == cellDate.day;

          return _buildCalendarDay(
            cellDate,
            isCurrentMonth,
            isToday,
            hasEntry,
            isSelected,
          );
        },
      ),
    );
  }

  Widget _buildCalendarDay(
    DateTime date,
    bool isCurrentMonth,
    bool isToday,
    bool hasEntry,
    bool isSelected,
  ) {
    Color backgroundColor = Colors.transparent;
    Color textColor = isCurrentMonth
        ? const Color(0xFF2C2C2C)
        : const Color(0xFF2C2C2C).withValues(alpha: 0.3);

    if (isToday) {
      backgroundColor = const Color(0xFF006994); // Jaderská modrá
      textColor = Colors.white;
    } else if (isSelected) {
      backgroundColor = const Color(0xFF2E8B8B); // Středomořská tyrkysová
      textColor = Colors.white;
    } else if (hasEntry) {
      backgroundColor = const Color(
        0xFFFF6B35,
      ).withValues(alpha: 0.2); // Oranžová západ slunce
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCalendarDate = date;
        });
        _showDayEntries(date);
      },
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: hasEntry && !isToday && !isSelected
              ? Border.all(color: const Color(0xFFFF6B35), width: 2)
              : null,
        ),
        child: Stack(
          children: [
            Center(
              child: Text(
                date.day.toString(),
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.w500,
                  color: textColor,
                ),
              ),
            ),
            if (hasEntry)
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: isToday || isSelected
                        ? Colors.white
                        : const Color(0xFFFF6B35),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showDayEntries(DateTime date) {
    final dayEntries = _entries
        .where(
          (entry) =>
              entry.date.year == date.year &&
              entry.date.month == date.month &&
              entry.date.day == date.day,
        )
        .toList();

    if (dayEntries.isEmpty) {
      // Zobrazit možnost vytvoření nového záznamu
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            DateFormat('d. MMMM yyyy').format(date),
            style: GoogleFonts.playfairDisplay(fontWeight: FontWeight.bold),
          ),
          content: const Text('Pro tento den nemáte žádné záznamy.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Zrušit'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _createNewEntryForDate(date);
              },
              child: const Text('Vytvořit záznam'),
            ),
          ],
        ),
      );
    } else {
      // Zobrazit záznamy pro daný den
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildDayEntriesSheet(date, dayEntries),
      );
    }
  }

  Widget _buildDayEntriesSheet(DateTime date, List<DiaryEntry> entries) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Color(0xFFF8F6F0), // Krémový papír
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    DateFormat('d. MMMM yyyy').format(date),
                    style: GoogleFonts.playfairDisplay(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF006994),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _createNewEntryForDate(date),
                  icon: const Icon(Icons.add, color: Color(0xFF006994)),
                ),
              ],
            ),
          ),

          // Záznamy
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: entries.length,
              itemBuilder: (context, index) => _buildEntryCard(entries[index]),
            ),
          ),
        ],
      ),
    );
  }

  void _createNewEntryForDate(DateTime date) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DiaryEntryEditScreen(template: {'date': date}),
      ),
    );
  }

  // Watercolor pozadí Chorvatska podle obrázku
  Widget _buildWatercolorBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: const Alignment(0.3, -0.2),
          radius: 1.5,
          colors: [
            const Color(0xFF006994).withValues(alpha: 0.3), // Adriatic blue
            const Color(
              0xFF2E8B8B,
            ).withValues(alpha: 0.2), // Mediterranean turquoise
            const Color(0xFF006994).withValues(alpha: 0.1),
            Colors.transparent,
          ],
          stops: const [0.0, 0.4, 0.7, 1.0],
        ),
      ),
      child: CustomPaint(painter: WatercolorPainter(), size: Size.infinite),
    );
  }

  // Obsah telefonu podle obrázku
  Widget _buildPhoneContent() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFFF8F6F0), // Krémový papír
            Color(0xFFE8E6E0), // Světlejší krémová
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(31),
      ),
      child: Stack(
        children: [
          // Watercolor pozadí v telefonu
          Positioned(
            top: 80,
            left: -50,
            right: -50,
            bottom: 100,
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: const Alignment(0.0, 0.3),
                  radius: 1.2,
                  colors: [
                    const Color(0xFF006994).withValues(alpha: 0.4), // Moře
                    const Color(0xFF2E8B8B).withValues(alpha: 0.3), // Ostrovy
                    const Color(0xFF006994).withValues(alpha: 0.2),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.3, 0.6, 1.0],
                ),
              ),
            ),
          ),

          // Obsah telefonu
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Nadpis "Deníkový zápis"
                Text(
                  'Deníkový zápis',
                  style: GoogleFonts.playfairDisplay(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF006994),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 30),

                // Hlasové tlačítko podle obrázku
                Center(
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF6B35), // Oranžová západ slunce
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFFF6B35).withValues(alpha: 0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: const Icon(Icons.mic, color: Colors.white, size: 28),
                  ),
                ),

                const SizedBox(height: 20),

                // Text "Naslouchá..."
                Center(
                  child: Text(
                    'Naslouchá...',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ),

                const SizedBox(height: 30),

                // Text deníku podle obrázku
                Text(
                  'Dnes jsme jeli trajektem na ostrov Hvar a měli jsme úžasný den na pláži.',
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    color: const Color(0xFF2C2C2C),
                    height: 1.5,
                  ),
                ),

                const Spacer(),

                // Text "Napište shrnutí dne"
                Text(
                  'Napište shrnutí dne',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF666666),
                  ),
                ),

                const SizedBox(height: 15),

                // Tagy nálad podle obrázku
                Row(
                  children: [
                    _buildMoodTag('Šťastný', const Color(0xFFFF6B35)),
                    const SizedBox(width: 8),
                    _buildMoodTag('Vděčný', const Color(0xFF2E8B8B)),
                    const SizedBox(width: 8),
                    _buildMoodTag('Pokojný', const Color(0xFF006994)),
                  ],
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Tag nálady podle obrázku
  Widget _buildMoodTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  // Watercolor mapa Chorvatska pro kalendář
  Widget _buildWatercolorCroatiaMap() {
    return Container(
      width: 200,
      height: 300,
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: const Alignment(-0.3, 0.2),
          radius: 1.0,
          colors: [
            const Color(0xFF006994).withValues(alpha: 0.2), // Adriatic blue
            const Color(
              0xFF2E8B8B,
            ).withValues(alpha: 0.15), // Mediterranean turquoise
            Colors.transparent,
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: CustomPaint(
        painter: CroatiaMapPainter(),
        size: const Size(200, 300),
      ),
    );
  }
}

// Watercolor painter pro pozadí
class WatercolorPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 2;

    // Kreslení watercolor efektu - ostrovy a moře
    final path1 = Path();
    path1.moveTo(size.width * 0.2, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.2,
      size.width * 0.6,
      size.height * 0.35,
    );
    path1.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.5,
      size.width * 0.9,
      size.height * 0.7,
    );
    path1.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.8,
      size.width * 0.3,
      size.height * 0.75,
    );
    path1.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.6,
      size.width * 0.2,
      size.height * 0.3,
    );

    paint.color = const Color(0xFF006994).withValues(alpha: 0.2);
    canvas.drawPath(path1, paint);

    // Druhý ostrov
    final path2 = Path();
    path2.moveTo(size.width * 0.6, size.height * 0.1);
    path2.quadraticBezierTo(
      size.width * 0.8,
      size.height * 0.05,
      size.width * 0.95,
      size.height * 0.2,
    );
    path2.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.4,
      size.width * 0.7,
      size.height * 0.3,
    );
    path2.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.2,
      size.width * 0.6,
      size.height * 0.1,
    );

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.25);
    canvas.drawPath(path2, paint);

    // Třetí ostrov
    final path3 = Path();
    path3.moveTo(size.width * 0.1, size.height * 0.8);
    path3.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.85,
      size.width * 0.4,
      size.height * 0.9,
    );
    path3.quadraticBezierTo(
      size.width * 0.2,
      size.height * 0.95,
      size.width * 0.05,
      size.height * 0.9,
    );
    path3.quadraticBezierTo(
      size.width * 0.08,
      size.height * 0.85,
      size.width * 0.1,
      size.height * 0.8,
    );

    paint.color = const Color(0xFF006994).withValues(alpha: 0.15);
    canvas.drawPath(path3, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Croatia map painter pro kalendář
class CroatiaMapPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..strokeWidth = 1.5;

    // Kreslení zjednodušené mapy Chorvatska
    final path = Path();

    // Istrie (severní část)
    path.moveTo(size.width * 0.2, size.height * 0.1);
    path.quadraticBezierTo(
      size.width * 0.35,
      size.height * 0.05,
      size.width * 0.5,
      size.height * 0.15,
    );
    path.lineTo(size.width * 0.45, size.height * 0.25);
    path.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.2,
      size.width * 0.2,
      size.height * 0.1,
    );

    paint.color = const Color(0xFF006994).withValues(alpha: 0.3);
    canvas.drawPath(path, paint);

    // Dalmatské pobřeží
    final coastPath = Path();
    coastPath.moveTo(size.width * 0.3, size.height * 0.3);
    coastPath.quadraticBezierTo(
      size.width * 0.25,
      size.height * 0.5,
      size.width * 0.35,
      size.height * 0.7,
    );
    coastPath.quadraticBezierTo(
      size.width * 0.4,
      size.height * 0.85,
      size.width * 0.5,
      size.height * 0.9,
    );
    coastPath.lineTo(size.width * 0.55, size.height * 0.85);
    coastPath.quadraticBezierTo(
      size.width * 0.6,
      size.height * 0.6,
      size.width * 0.55,
      size.height * 0.4,
    );
    coastPath.quadraticBezierTo(
      size.width * 0.45,
      size.height * 0.25,
      size.width * 0.3,
      size.height * 0.3,
    );

    paint.color = const Color(0xFF2E8B8B).withValues(alpha: 0.25);
    canvas.drawPath(coastPath, paint);

    // Ostrovy
    final islandPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = const Color(0xFF006994).withValues(alpha: 0.4);

    // Hvar
    canvas.drawCircle(
      Offset(size.width * 0.15, size.height * 0.6),
      3,
      islandPaint,
    );

    // Korčula
    canvas.drawCircle(
      Offset(size.width * 0.1, size.height * 0.75),
      2.5,
      islandPaint,
    );

    // Brač
    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.55),
      2,
      islandPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Placeholder obrazovky pro navigaci
class DiaryEntryEditScreen extends StatelessWidget {
  final Map<String, dynamic>? template;

  const DiaryEntryEditScreen({super.key, this.template});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Nový záznam')),
      body: const Center(child: Text('Obrazovka pro editaci záznamu')),
    );
  }
}

class DiaryEntryDetailScreen extends StatelessWidget {
  final DiaryEntry entry;

  const DiaryEntryDetailScreen({super.key, required this.entry});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(entry.title)),
      body: const Center(child: Text('Detail záznamu')),
    );
  }
}
